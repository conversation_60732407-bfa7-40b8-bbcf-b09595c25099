import { EyeOutlined } from "@ant-design/icons";
import { FloatButton, Tooltip } from "antd";
import { FC } from "react";
import { useTranslation } from "react-i18next";

const InfoPreviewButton: FC<{ tourRef: any; setIsShowTour: any }> = ({
  tourRef,
  setIsShowTour,
}) => {
  const { t } = useTranslation();
  return (
    <>
      <Tooltip title={t("workFlow.addTransition")}>
        <span
          ref={tourRef}
          className=" !w-[30px] !h-[30px] !flex items-center justify-center !border !border-gray-300 !bg-white !cursor-pointer"
          onClick={() => {
            setIsShowTour(true);
          }}
          style={{
            top: "10%", // üst kenara hizala
            position: "fixed", // sabitle
            left: "18.5%", // yatayda ortala
            transform: "translateX(-50%)", // tam ortalamak için
            zIndex: 1000, // üstte kalması için isteğe bağlı
          }}
        >
          <EyeOutlined />
        </span>
      </Tooltip>
    </>
  );
};

export default InfoPreviewButton;
