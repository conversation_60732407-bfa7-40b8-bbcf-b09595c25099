import { List, Typography, Col, Row } from "antd";
import { ArrowLeftOutlined, BranchesOutlined } from "@ant-design/icons";
import { useGetEdgeRuleTypes } from "../../../ServerSideStates";
import { FC, useState } from "react";
import DynamicForm from "./DynamicForm";
import { t } from "i18next";

const { Text } = Typography;

const RulesIndex:FC<{onFinish:any,selectedRecord?:any,setSelectedRecord:any}> = ({onFinish,selectedRecord,setSelectedRecord}) => {
  const ruleTypes = useGetEdgeRuleTypes();
 
  const { Text } = Typography;
  const data = selectedRecord
    ? JSON.parse(selectedRecord?.FormConfigurationJson)
    : null;

  

  return (
    <Col xs={24} className="!mt-8" >
      <Row>
        {selectedRecord ? (
          <>
            <Col xs={24}>
              <Row gutter={[0,10]}>
                <Col xs={24}  >

                <span
                className="!flex gap-1 !cursor-pointer"
                onClick={()=>{
                  setSelectedRecord(null)
                }}
                >

                <ArrowLeftOutlined className="!text-base !text-gray-400 "
               
                />
                <Text className="!text-gray-400" >{t("workFlow.back")}</Text>
                </span>
                </Col>
                <Col xs={24} className="!flex flex-col ">
                
                  <Text>{data?.title || ""}</Text>
                  <Text className="!text-xs !text-gray-400">
                    {data?.description || ""}
                  </Text>
                </Col>
               
                <Col xs={24} className="" >
                <DynamicForm selectedRecord={selectedRecord} onFinish={()=>{
                  setSelectedRecord(null)
                  onFinish()
                }} />
                </Col>
              </Row>
            </Col>
          </>
        ) : (
          <>
            <Col xs={24}>
              <List
                loading={ruleTypes.isLoading || ruleTypes.isFetching}
                dataSource={ruleTypes?.data?.Value || []}
                className=""
                renderItem={(item: any) => (
                  <>
                    <List.Item
                      className="!bg-gray-100 !rounded-sm  !w-full cursor-pointer"
                      onClick={async () => {
                        await setSelectedRecord(item);
                      }}
                    >
                      <Col xs={24} className="!flex">
                        <div className="!flex items-center justify-center !w-[70px] !h-[50px] gap-2 ">
                          <BranchesOutlined className="!text-black !text-2xl" />
                        </div>
                        <div>
                          <div>
                            <Text className="!text-black ">
                              {item?.DisplayName || ""}
                            </Text>
                          </div>
                          <div>
                            <Text className="!text-xs !text-gray-500">
                              {item.Description || ""}
                            </Text>
                          </div>
                        </div>
                      </Col>
                    </List.Item>
                  </>
                )}
              />
            </Col>
          </>
        )}
      </Row>
    </Col>
  );
};

export default RulesIndex;
