import useMazakaForm from "@/hooks/useMazakaForm";
import { Col, Form, Modal, Row } from "antd";
import { useQueryClient } from "react-query";
import endPoints from "../../../EndPoints";
import { useTranslation } from "react-i18next";
import { FC, useEffect } from "react";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { addNodeToWorkFlow, deleteNode, updateNodeOnWorkFlow } from "../../../Services";
import { determineNodeStatus } from "@/helpers/WorkFlow";
import { MazakaSelect } from "@/apps/Common/MazakaSelect";
import { useParams } from "react-router-dom";
import { useGetWorkFlowNodes } from "../../../ServerSideStates";

const AddOrUpdateNode: FC<{
  onFinish: () => void;
  selectedNode?: any;
}> = ({ onFinish, selectedNode }) => {
  const [form] = Form.useForm();
  const { formActions, mazakaForm } = useMazakaForm(form);
  const { t } = useTranslation();
  const types = determineNodeStatus("select", null, t);
  const {workFlowId} = useParams()
  const ZOOM_STORAGE_KEY = "react-flow-zoom";
   const workFlowNodes = useGetWorkFlowNodes({
      PageNumber: 1,
      PageSize: 100,
      FlowId: workFlowId,
    });
  const savedZoomViewPort = localStorage.getItem(ZOOM_STORAGE_KEY);
  const currentViewPort = savedZoomViewPort&&savedZoomViewPort!=="undefined"
  ? JSON.parse(savedZoomViewPort)
  : {x: 692.7124424312469, y: 833.0128317201845, zoom: 1.1611787790390786};
  const queryClient = useQueryClient();

  useEffect(() => {
    form.setFieldsValue({ Name:selectedNode?.data?.label,NodeType:selectedNode?.data?.type==="Start"?1:selectedNode?.data?.type==="End"?3:selectedNode?.data?.type==="Process"?2:undefined });
  }, [selectedNode]);

  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    const formValues = form.getFieldsValue();

    try {
      formValues["FlowId"] = workFlowId
      if (selectedNode) {
        console.log("selectedNode",selectedNode)
        formValues["Id"]= selectedNode?.id
        formValues["PositionX"]=selectedNode?.position?.x
        formValues["PositionY"]=selectedNode?.position?.y
        await updateNodeOnWorkFlow(formValues)
      } else {
        let allNodes = workFlowNodes?.data?.Value
        const maxXNode = allNodes.reduce((prev:any, current:any) => {
          return (current.PositionX > prev.PositionX) ? current : prev;
        });
      
        const newX = maxXNode.PositionX + 200;
        const newY = maxXNode.PositionY;
      
        await addNodeToWorkFlow({
          ...formValues,
          PositionX: newX,
          PositionY: newY,
        });
      }
      mazakaForm.setSuccess(2000, () => t("form.transactionSuccessful"));
      openNotificationWithIcon("success", t("form.transactionSuccessful"));
      form.resetFields();

      queryClient.resetQueries({
        queryKey: endPoints.getWorkFlowNodes,
        exact: false,
      });
      onFinish();
      
    } catch (error) {
      showErrorCatching(error, mazakaForm, true, t);
    }
  };

  const deleteNodeConfirm = () => {
    Modal.confirm({
      title: t("profession.warning"),
      icon: null,
      content: t("profession.deleteModalDesc"),
      okText: t("profession.delete"),
      cancelText: t("profession.cancel"),
      onOk: async () => {
        try {
          await deleteNode({Id:selectedNode?.id});
          openNotificationWithIcon("success", t("form.transactionSuccessful"));
          queryClient.resetQueries({
            queryKey: endPoints.getWorkFlowNodes,
            exact: false,
          });
          form.resetFields();
          onFinish();
        } catch (error: any) {
          showErrorCatching(error, null, false, t);
        }
      },
    });
  };

  console.log("selected node",selectedNode)
  return (
    <>
      <Col xs={24}>
        <MazakaForm
          form={form}
          onFinish={handleOnFinish}
          submitButtonVisible={false}
        >
          <Row gutter={[0, 10]}>
            <MazakaInput
              label={t("workFlow.name")}
              placeholder={t("workFlow.name")}
              xs={24}
              name={"Name"}
              rules={[{required:true,message:""}]}
            />
            <MazakaSelect
              label={t("workFlow.type")}
              placeholder={t("workFlow.type")}
              xs={24}
              name={"NodeType"}
              rules={[{required:true,message:""}]}
              options={types}
            />
            <Col xs={24} className="!flex gap-1" >
              <MazakaButton
                htmlType="submit"
                processType={formActions.submitProcessType}
                status="save"
              >
                {t("workFlow.save")}
              </MazakaButton>
              {selectedNode&& selectedNode?.type !== "connect" && (
                <MazakaButton
                  htmlType="button"
                  status="error"
                  onClick={deleteNodeConfirm}
                >
                  {t("workFlow.deleteStep")}
                </MazakaButton>
              )}
            </Col>
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default AddOrUpdateNode;
