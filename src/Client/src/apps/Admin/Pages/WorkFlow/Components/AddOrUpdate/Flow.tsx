import { useEffect, useRef, useState } from "react";
import {
  ReactFlow,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  Node,
  MiniMap,
  Edge,
  useReactFlow,
} from "@xyflow/react";

import "@xyflow/react/dist/style.css";
import { Drawer, Tour } from "antd";
import ResizeableNodeSelected from "./Nodes/ResizeableNodeSelected";
import { useGetNodeEdges, useGetWorkFlowNodes } from "../../ServerSideStates";
import { useParams, useSearchParams } from "react-router-dom";
import AddOrUpdateNodeButton from "./Nodes/AddOrUpdateNodeButton";
import AddOrUpdateNode from "./Nodes/AddOrUpdateNode";
import AddOrUpdateEdgeButton from "./Edges/AddOrUpdateEdgeButton";
import AddOrUpdateEdge from "./Edges/AddOrUpdateEdge";
import { useTranslation } from "react-i18next";

import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { updateNodeOnWorkFlow } from "../../Services";
import { TourProps } from "antd/lib";
import { useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import dayjs from "dayjs";
import InfoPreviewButton from "../InfoPreview";

const EdgeTypesFlow = () => {
  const ZOOM_STORAGE_KEY = "react-flow-zoom";
  const {userInfoes} = useSelector((state:RootState)=>state.profile)
  const savedZoomViewPort = localStorage.getItem(ZOOM_STORAGE_KEY);
  const defaultZoom = savedZoomViewPort&&savedZoomViewPort!=="undefined"
  ? JSON.parse(savedZoomViewPort)
  : {x: 692.7124424312469, y: 833.0128317201845, zoom: 1.1611787790390786};
  const [viewPort,setViewPort] = useState(defaultZoom)
  const ref1 = useRef(null);
  const ref2 = useRef(null);
  const ref3 = useRef(null);
  const ref4 = useRef(null);
  const ref5 = useRef(null);
  const ref6 = useRef(null);


  const [isShowTour, setIsShowTour] = useState(false);
  const [searchParams, setSearchParams] = useSearchParams();
  const { t } = useTranslation();
  const [isShowEditNodeDrawer, setIsShowEditNodeDrawer] = useState(false);
  const [isShowEditEdgeDrawer, setIsShowEditEdgeDrawer] = useState(false);
  const [selectedEdge, setSelectedEdge] = useState<Edge | null>(null);
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [selectedNode, setSelectedNode] = useState<any>(null);
  const { workFlowId } = useParams();
 
  const steps: TourProps["steps"] = [
    {
      title: t("workFlow.tourStep1Title"),
      description: t("workFlow.tourStep1Desc"),
     
      target: () => ref1.current,
    },
    {
      title: t("workFlow.tourStep2Title"),
      description: t("workFlow.tourStep2Desc"),
      target: () => ref2.current,
    },
    {
      title: t("workFlow.tourStep3Title"),
      description: t("workFlow.tourStep3Desc"),
      target: () => ref3.current,
    },
    {
      title: t("workFlow.tourStep4Title"),
      description: t("workFlow.tourStep4Desc"),
      target: () => ref4.current,
    },
    {
      title: t("workFlow.tourStep5Title"),
      description: t("workFlow.tourStep5Desc"),
      target: () => ref5.current,
    },
    {
      title: t("workFlow.tourStep6Title"),
      description: t("workFlow.tourStep6Desc"),
      target: () => ref6.current,
    },
   
  ];
 
  const workFlowNodes = useGetWorkFlowNodes({
    PageNumber: 1,
    PageSize: 100,
    FlowId: workFlowId,
  });
  const nodeEdges = useGetNodeEdges({
    PageNumber: 1,
    PageSize: 100,
  });

  useEffect(() => {
    if (workFlowNodes.data && nodeEdges?.data) {
      const allNodes = workFlowNodes.data?.Value || [];
      const allEdges = nodeEdges.data?.Value || [];
  
      // Edge'lerde geçen tüm source ve target node ID'lerini topla
      const connectedNodeIds = new Set<string>();
      allEdges.forEach((edge: any) => {
        if (edge.FromNodeId) connectedNodeIds.add(edge.FromNodeId);
        if (edge.ToNodeId) connectedNodeIds.add(edge.ToNodeId);
      });
  
      const formatedNodeData = allNodes.map((item: any) => {
        let backgroundColor = "";
        let color = "";
  
        if (item.Type === "Start") {
          backgroundColor = "#d1d5db";
          color = "black";
        } else if (item.Type === "Process") {
          backgroundColor = "white";
          color = "black";
        } else if (item.Type === "End") {
          backgroundColor = "#0096d1";
          color = "white";
        }
  
        // Node bu bağlantılarda yer almıyorsa kırmızı border ver
        const isConnected = connectedNodeIds.has(item.Id);
        const borderStyle = isConnected ? {} : { outline: "1px solid red", outlineOffset: "4px", };
  
        return {
          id: item?.Id,
          data: {
            label: item?.Name || "",
            type: item.Type,
            x: item?.PositionX,
            y: item?.PositionY,
            flowId: item?.FlowId,
          },
          position: { x: item?.PositionX || 0, y: item?.PositionY || 0 },
          type: "ResizeableNodeSelected",
          style: {
            backgroundColor,
            color,
            ...borderStyle,
          },
        };
      });
  
      setNodes(formatedNodeData);
  
      const formattedNodeEdges = allEdges.map((item: any) => ({
        type: "smoothstep",
        markerEnd: {
          type: "arrowclosed",
        },
        source: item.FromNodeId,
        target: item.ToNodeId,
        id: item.Id,
        label: item.Name,
        animated: true,
      }));
  
      setEdges(formattedNodeEdges);
    }
  }, [workFlowNodes.data, nodeEdges.data]);
  

  const onConnect = async (edge: any) => {
    const sourceNode = nodes.find((node:any) => node.id === edge.source);
    const targetNode = nodes.find((node:any) => node.id === edge.target);
    await setSelectedEdge({
      ...edge,
      type: "connect",
      sourceNode,
      targetNode
    });
    setIsShowEditEdgeDrawer(true);
  };

  const handleNodeClick = async (_: any, node: Node) => {
    if (node?.data?.type !== "Start") {
      await setSelectedNode(node);
      setIsShowEditNodeDrawer(true);
      setToStatus("");
    }
  };

  const onEdgeClick = async (_: any, edge: Edge) => {
    await setSelectedEdge(edge);
    setIsShowEditEdgeDrawer(true);
  };
  const nodeTypes = {
    ResizeableNodeSelected,
  };

  const nodeColor = (node: any) => {
    if (node?.data?.type === "Start") {
      return "#d1d5db";
    }
    if (node?.data?.type === "End") {
      return "#0096d1";
    }
    return "black";
  };

  const handleDropNode = async (event: any, node: any) => {
    const currentData = { ...node?.data };
    const currentPosition = node.position;

   
    try {
      const data = {
        FlowId: currentData.FlowId,
        Name: currentData?.label,
        PositionX: currentPosition?.x,
        PositionY: currentPosition?.y,
        NodeType:
          currentData?.type === "Start"
            ? 1
            : currentData?.type === "End"
            ? 3
            : 2,
        Id: node?.id,
      };
      await updateNodeOnWorkFlow(data);
    } catch (error) {
      showErrorCatching(error, null, false, t);
    }
  };




  useEffect(() => {
    if (userInfoes) {
      const insertDate = userInfoes?.InsertDate;
      if (insertDate && dayjs().diff(dayjs(insertDate), "day") < 30) {
        const isViewed = localStorage.getItem("isViewedTour");
        setIsShowTour(typeof isViewed==="boolean"?Boolean(isViewed):false);
      }
    }
  }, [userInfoes]);
  

  return (
    <div className="!h-screen !flex" >
      <div style={{ flexGrow: 1 }}>
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onEdgeClick={onEdgeClick}
          onConnect={onConnect}
          onNodeClick={handleNodeClick}
          nodeTypes={nodeTypes}
          onNodeDragStop={handleDropNode}
          // fitView
         viewport={viewPort}
          defaultEdgeOptions={{ type: "smoothstep" }}
          style={{ backgroundColor: "#F7F9FB" }}
          snapToGrid={true}
          ref={ref3}
          onViewportChange={(changedViewport:any)=>{
   
            setViewPort(changedViewport)
            localStorage.setItem(ZOOM_STORAGE_KEY,JSON.stringify(changedViewport))
    
          }}
        >
          {isShowTour ? (
            <>
              <div
                ref={ref4}
                className="react-flow__panel react-flow__minimap !mb-5 bottom right w-[230px] !h-[200px] !bg-transparent"
              >
                <MiniMap
                  nodeColor={nodeColor}
                  nodeStrokeWidth={3}
                  zoomable
                  pannable
                />
              </div>
            </>
          ) : (
            <>
              <MiniMap
                nodeColor={nodeColor}
                nodeStrokeWidth={3}
                zoomable
                pannable
                className="!mb-24"
              />
            </>
          )}

          {isShowTour ? (
            <>
              <div
                ref={ref5}
                className="react-flow__panel react-flow__minimap !mb-5 bottom left w-[60px] !h-[150px] !bg-transparent"
              >
                <Controls className="" />
              </div>
            </>
          ) : (
            <>
              <Controls className="!mb-24" />
            </>
          )}

          <Background />
        </ReactFlow>

        <div>
          <AddOrUpdateEdgeButton tourRef={ref1} />
          <AddOrUpdateNodeButton tourRef={ref2} />
          <InfoPreviewButton tourRef={ref6}  setIsShowTour={setIsShowTour} />
        </div>
      </div>

      <Drawer
        title={t("workFlow.editStep")}
        open={isShowEditNodeDrawer}
        onClose={() => {
          setIsShowEditNodeDrawer(false);
        }}
      >
        <AddOrUpdateNode
          selectedNode={selectedNode}
          onFinish={() => {
            setIsShowEditNodeDrawer(false);
          }}
        />
      </Drawer>

      <Drawer
        title={t("workFlow.editTransition")}
        open={isShowEditEdgeDrawer}
        onClose={() => {
          const newParams = new URLSearchParams(searchParams.toString());
          newParams.delete("edgeId");
          setSearchParams(newParams);
          setIsShowEditEdgeDrawer(false);
        }}
        width={"50%"}
      >
        <AddOrUpdateEdge
          selectedRecord={selectedEdge}
          setSelectedRecord={setSelectedEdge}
          onFinish={() => {
            const newParams = new URLSearchParams(searchParams.toString());
            newParams.delete("edgeId");
            setSearchParams(newParams);
            setIsShowEditEdgeDrawer(false);
          }}
        />
      </Drawer>
      <Tour
        open={isShowTour}
        onClose={() => {
          setIsShowTour(false);
          localStorage.setItem("isViewedTour","true")
          window.scrollTo(0, 0);
        }}
        steps={steps}
        
      />
    </div>
  );
};

export default EdgeTypesFlow;
