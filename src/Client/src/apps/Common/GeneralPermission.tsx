import  { FC, useEffect, useState } from "react";
import { Col, Form, TreeSelect, Typography } from "antd";
import { useGetAllPermissions } from "@/apps/Admin/Pages/Authority/ServerSideStates";
import { useTranslation } from "react-i18next";
import { MazakaSelectProps } from "@/models/Client/MazakaSelectProps";

const GeneralPermissions: FC<MazakaSelectProps> = (props) => {
  const { t } = useTranslation();
  const allPermissions = useGetAllPermissions();
  const [treeData, setTreeData] = useState<any[]>([]);
  const [value, setValue] = useState<any[]>([]);

  const { Text } = Typography;

  function buildPermissionTree(data: any) {
    const map = new Map();
    const roots = [];

    for (const item of data) {
      map.set(item.Id, {
        title: item?.Name,
        
        value: item.Id,
        key: item.Id,
        children: [],
        disabled: false,
      });
    }

    for (const item of data) {
      const node = map.get(item.Id);
      if (item.TopPermissionId && map.has(item.TopPermissionId)) {
        map.get(item.TopPermissionId).children.push(node);
      } else {
        roots.push(node);
      }
    }

    return roots;
  }

  useEffect(() => {
    setTreeData(buildPermissionTree(allPermissions?.data?.Value || []));
  }, [allPermissions.data]);

  const onChange = (newValue: any) => {
    setValue(newValue);
  
  };

  return (
    <Col
      span={24}
      className={props.colClassName}
      md={props.md}
      sm={props.sm}
      lg={props.lg}
      xl={props.xl}
      xxl={props.xxl}
      xs={props.xs}
    >
      <Form.Item
        className={props.className}
        name={props.name}
        initialValue={props.initialValue}
        valuePropName={props.valuePropName}
        rules={props.rules}

        label={props.label}
        colon={props.colon}
        labelAlign={props.labelAlign}
        labelCol={props.labelCol}
        wrapperCol={props.wrapperCol}
        hasFeedback={props.hasFeedback}
        noStyle={props.noStyle}
        tooltip={props.tooltip}
      >
        <TreeSelect
          treeData={treeData}
         treeCheckStrictly={true}
          value={value}
          loading={allPermissions.isLoading || allPermissions.isFetching}
          onChange={onChange}
          treeCheckable={true}
         
          placeholder={t("authority.selectPermissions")}
          style={{ width: "100%" }}
          allowClear
          multiple={props.multiple}
        />
      </Form.Item>
    </Col>
  );
};

export default GeneralPermissions;
