import i18n from "i18next";
import { initReactI18next } from "react-i18next";

const defaultLang = "TR";
const lang = localStorage.getItem("lang") || defaultLang;

const appVersion = "1.0.0";
const storedResource = localStorage.getItem("langResource");

const defaultResources = {
  TR: {
    translation: {
      welcome: "Hoşgeldiniz",
      login: "<PERSON><PERSON><PERSON> Yap",
      clearFilterButton: "Filtreyi Temizle",
      atusageProcessError:
        "Bu öğe başka işlemler tarafından kullanılıyor, silinemez",

      generalSearch: "Ara...",
      notification: {
        notifications: "<PERSON><PERSON>dir<PERSON><PERSON>",
        readed: "<PERSON>unan<PERSON>",
        unReaded: "<PERSON><PERSON><PERSON>yanlar",
        title: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
        message: "Mesaj",
        user: "<PERSON>llanı<PERSON><PERSON>",
        date: "<PERSON><PERSON><PERSON>",
        markAllNotificationsRead: "Tüm bildirimleri okundu olarak işaretle",
        markAllMyNotificationRead:
          "Bana ait tüm bildirimleri okundu olarak işaretle",
        approveChangeAllNotificationStatus:
          "Tüm bildirimlerin durumu değişecek, onaylıyor musunuz?",
      },
      team: {
        team: "Takım",
      },
      dashboard: {
        topUsersByTaskCount: "Görev Sayısına Göre Kullanıcılar",
        dashboard: "Dashboard",
        statusOfTickets: "Ticketlerin Durumu",
        priorityStatusTasks: "Görevlerin Öncelik Durumu",
        unassigned: "Atanmamış",
        searchAssignmentGraph: "Arama Atama Grafiği",
        unAssignedDesc: "Görev henüz kimseye atanmadı",
        countinue: "Devam Ediyor",
        countinueDesc: "Görev şu anda devam ediyor",
        low: "Düşük",
        medium: "Orta",
        high: "Yüksek",
        critical: "Kritik",
        done: "Tamamlandı",
        doneDesc: "Görev tamamlandı",
        urgent: "Acil",
        urgentDesc: "Görev tamamlandı",
      },
      recording: {
        recordings: "Kayıtlar",

        callerName: "Arayan Adı",
        caller: "Arayan",
        callee: "Aranan",
        calleePhoneNumber: "Aranan Telefon Numarası",
        callerPhoneNumber: "Arayan Telefon Numarası",
        calleeName: "Aranan Adı",
        direction: "Yön",
        inbound: "Gelen",
        outbound: "Giden",
        startTime: "Başlama Zamanı",
        endTime: "Bitiş Zamanı",
        answeredTime: "Cevaplanan Zaman",
        talkDurationInSeconds: "Konuşma Süresi (saniye)",
        totalDurationInSeconds: "Toplam Süre (saniye)",
        copyDownloadLink: "İndirme Bağlantısını Kopyala",
        isAnswered: "Cevaplandı mı",
        extension: "Dahili",
        transcription: "Yazıya Döküm",
        export: "Dışa Aktar",
        delete: "Sil",
        missed: "Cevapsız",
        answerd: "Cevaplandı",
        callStatus: "Arama Durumu",
        showSummaryCall: "Aramanın Özetini Göster",
        summary: "Özet",
        interviewSummary: "Görüşme Özeti",
        fullContent: "Tam İçerik",
        download: "Download",
        invalidLink: "Link Geçersiz",
        callDetails: "Arama Detayı",
        sourceParticipantName: "Kaynak Katılımcı Adı",
        sourceParticipantPhoneNumber: "Kaynak Katılımcı TelefonNumarası",
      },

      general: {
        pageNotFound: "Sayfa Bulunamadı",
        gotoToDashboard: "Ana Sayfaya Git",
        forbiddenDesc: "Bu Sayfaya Erişim İzniniz Bulunmuyor",
      },
      header: {
        takeBreak: "Mola al",
        myBreaks: "Molalarım",
        avaliable: "Uygun",
        away: "Dışarıda",
        doNotDistrub: "Rahatsız Etmeyin",
        lunch: "Öğle Yemeği",
        businessTrip: "İş Seyahati",
        logout: "Çıkış Yap",
        settings: "Ayarlar",
      },
      account: {
        login: "Giriş Yap",
        inCorrectEmailOrPass: "E-postanız veya şifreniz hatalı.",
        email: "E-posta",
        password: "Şifre",
        forgotPassword: "Şifremi Unuttum",
        sendEmail: "E-Posta Gönder",
        emailSent: "E-Posta Gönderildi",
        changePassword: "Şifreyi Değiştir",
        newPassword: "Yeni Şifre",
      },

      adminSidebar: {
        users: "Kullanıcılar",
        callReports: "Çağrı Raporları",
        fileManager: "Dosya Yöneticisi",
        customers: "Müşteriler",
        profession: "Meslek",
        sectors: "Sektör",
        subjectTickets: "Konu Bildirimleri",
        ticket: "Ticket",
        task: "Görev",
        classification: "Sınıflandırma",
        tag: "Etiket",
        importData: "Veri Yükle",
        autoDialer: "Otomatik Arayıcı",
        workflow: "İş Akışı",
        pauseManagement: "Mola Yönetimi",
        department: "Departman",
        recordings: "Kayıtlar",
        authority: "Yetki",
        roles: "Roller",
        report: "Rapor",
        languages: "Diller",
        customerSource: "Müşteri kaynağı",
      },
      leftMainSidebar: {
        admin: "Yönetici",
      },
      users: {
        users: "Kullanıcılar",
        addButton: "Ekle",
        deleteAllButton: "Sil",
        export: "Dışrıya Aktar",

        searchPlaceholder: "Ara",
        list: {
          user: "Kullanıcı",
          email: "E-posta",
          extension: "Dahili",
          department: "Departman",
          delete: "Sil",
          edit: "Güncelle",
          cancel: "Vazgeç",
          warning: "Uyarı",
          deleteModalDesc: "Bu öğe silinecek. Onaylıyor musunuz?",
        },
        add: {
          general: "Genel",
          permissions: "Yetkiler",
          options: "Seçenekler",
          extension: "Dahili",
          name: "Ad",
          surName: "Soyad",
          email: "E-posta",
          phone: "Telefon",
          password: "Şifre",
          status: "Durum",
          active: "Aktif",
          pasive: "Pasif",
          addUser: "Kullanıcı Ekle",
          editUser: "Kullanıcıyı Düzenle",
          save: "Kaydet",
          extensionNumberFindError: "Bu numara daha önce kayıtlıdır",
          passwordDesc: {
            desc1: "En az bir küçük harf bulunmalıdır.",
            desc2: "En az bir büyük harf bulunmalıdır.",
            desc3: "En az bir rakam bulunmalıdır.",
            desc4: "Toplamda en az 6 karakter olmalıdır.",
          },
        },
        filter: {
          filterData: "Verileri Filtrele",
          filterButton: "Filtrele",
        },
        import: {
          importData: "Veri Yükle",
          upload: "Yükle",
          uploaderFileTitle: "Dosyayı Yükleyin veya Sürükleyin",
          uploaderFileDesc: "Desteklenen dosya formatı: xlsx",
        },
      },

      customers: {
        customers: "Müşteriler",
        addButton: "Ekle",
        deleteAllButton: "Sil",
        export: "Dışrıya Aktar",
        import: "içe aktarmak",
        filter: "filtre",
        searchPlaceholder: "Ara",
        list: {
          customer: "Müşteri",
          customerPhone: "Müşteri Telefon Numarası",
          type: "Tip",
          sector: "Sektör",
          phone: "Telefon",
          classification: "Sınıflandırma",
          email: "E-posta",
          extension: "Dahili",
          department: "Departman",
          delete: "Sil",
          edit: "Güncelle",
          cancel: "Vazgeç",
          warning: "Uyarı",
          deleteModalDesc: "Bu öğe silinecek. Onaylıyor musunuz?",
        },
        add: {
          addressTitle: "Adres Başlığı",
          customerRepresentative: "Müşteri Temsilcileri",
          assignCustomerRepresentative: "Temsilciye Ata",
          general: "Genel",
          concat: "İletişim Listesi",
          dataSucessfullayUploader: "Verileriniz başarıyla yüklendi",
          permissions: "Yetkiler",
          options: "Seçenekler",
          extension: "Dahili",
          noneCallStatus: "Bilinmiyor",
          talking: "Görüşülüyor",
          answered: "Cevaplandı",
          missed: "Cevapsız",
          addNote: "Not Ekle",
          ended: "Sonlandırıldı",
          addManuallyAddress: "Manuel Adres Ekle",
          addAddressWithGoogle: "Google ile Adres Ekle",
          classificationDesc:
            "Sınıflandırma, müşterilerinizi davranış, değer veya özelliklerine göre gruplamanızı sağlar",
          classificationDescExample:
            "Örn: VIP, Sık Şikayet Eden, Kara Liste, Yüksek Potansiyelli gibi gruplar oluşturabilirsiniz",
          name: "Ad",
          surName: "Soyad",
          email: "E-posta",
          phone: "Telefon",

          customerKind: "Müşteri Türü",
          customer: "Müşteri",
          potentialCustomer: "Potansiyel Müşteri",
          renew: "Yeniden Kazanılan",
          active: "Aktif",
          passive: "Pasif",
          suspended: "Donduruldu",
          customerSource: "Müşteri Kaynağı",
          customerStatus: "Müşteri Durumu",
          taxOffice: "Vergi Dairesi",
          identification: "Kimlik",
          mainLanguage: "Ana Dil",
          availableLanguages: "Mevcut Diller",
          description: "Açıklama",
          individual: "Bireysel",
          corporate: "Kurumsal",
          companyName: "Firma Adı",
          topCompany: "Üst Firma",
          contact: "İletişim",
          title: "Başlık",
          languages: "Diller",
          save: "Kaydet",
          status: "Durum",
          segmentations: "Segmentasyonlar",
          addresses: "Adresler",
          country: "Ülke",
          state: "İl",
          city: "İlçe",
          neighborhood: "Mahalle",
          postCode: "Posta Kodu",
          redirection: "Yönlendirme",
          inform: "Bilgilendirme",
          taxNumber: "Vergi Numarası",
          identificationNumber: "Kimlik Numarası",
          mananger: "Yönetici",
          informationalEmails: "Bilgilendirme E-Postaları",
          call: "Arama",
          agent: "Temsilci",
          durations: "Süreler",
          direction: "Yön",
          outbound: "Giden",
          inbound: "Gelen",
          missed: "Cevapsız",
          address: "Adres",
          date: "Tarih",
          chat: "Sohbet",
          channel: "Kanal",
          users: "Kullanıcılar",
          notes: "Notlar",
          createdUser: "Oluşturan Kullanıcı",
          createdDate: "Oluşturulma Tarihi",
          password: "Şifre",
          status: "Durum",
          active: "Aktif",
          pasive: "Pasif",
          profession: "Meslek",
          addCustomer: "Müşteri Ekle",
          editCustomer: "Müşteriyi düzenle",
          save: "Kaydet",
          extensionNumberFindError: "Bu numara daha önce kayıtlıdır",
          passwordDesc: {
            desc1: "En az bir küçük harf bulunmalıdır.",
            desc2: "En az bir büyük harf bulunmalıdır.",
            desc3: "En az bir rakam bulunmalıdır.",
            desc4: "Toplamda en az 6 karakter olmalıdır.",
          },
        },
        filter: {
          filterData: "Verileri Filtrele",
          filterButton: "Filtrele",
          fullName: "Ad Soyad",
          identificationOrTaxNumber: "Kimlik veya Vergi Numarası",
          filterAddress: "Adresi Filtrele",
        },
        import: {
          importData: "Veri Yükle",
          upload: "Yükle",
          uploaderFileTitle: "Dosyayı Yükleyin veya Sürükleyin",
          uploaderFileDesc: "Desteklenen dosya formatı: xlsx",
        },
      },
      pause: {
        list: {
          breakRequests: "Mola Talepleri",
          finishBreakTime: " Molayı Bitir",
          estimatedFinish: "Tahmini Bitiş",
          counterTimerDesc: "Molanızın bitmesine kalan süre",
          addButton: "Ekle",
          breakType: "Mola Türü",
          awaitingApproval: "Onay Bekleyenler",
          accepted: "Kabul Edilenler",
          rejected: "Reddedilenler",
          started: "Başlayanlar",
          completed: "Tamamlananlar",
          canceled: "İptal Edilenler",
          seeAllStatuses: "Tüm Durumları Gör",
          staffName: "Personel Adı",
          date: "Tarih",
          start: "Başlangıç",
          end: "Bitiş",
          startDate: "Başlangıç Tarihi",
          startTime: "Başlangıç Saati",
          allowedMin: "İzin Verilen Dakika",
          approvalStatus: "Onay Durumu",
          staffDesc: "Personel Açıklaması",
          officialStatement: "Yetkili Açıklaması",
          acceptButton: "Kabul Et",
          rejectButton: "Reddet",
          updateBreakStatus: "Mola Durumunu Güncelle",
        },
      },

      chat: {
        chat: "Sohbet",
        chats: "Sohbetler",
        startChat: "Sohbet Başlat",
        newConversation: "Yeni Konuşma",
        start: "Başlat",
        newConversationInputDesc: "İsim, dahili numara veya e-posta yazın",
        next: "Sonra",
        previous: "Önce",
        countinue: "Devam Et",
        createGroupChat: "Grup Sohbeti Oluştur",
        archive: "Arşivle",
        endChat: "Sohbeti Bitir",
        openTicket: "Ticket Aç",
        block: "Engelle",
        cleanAllHistories: "Tüm Geçmişi Temizle",
      },
      calls: {
        calls: "Aramalar",
      },
      panel: {
        panel: "Panel",
      },

      authority: {
        "authority-main-title": "Yetkiler",
        user_name: "Kullanıcı Adı",
        department: "Departman",
        role: "Rol",
        email: "Email",
        status: "Durum",
        save: "Kaydet",
        selectAll: "Tümü Seç",
        removeSelectAll: "Tüm seçilileri kaldır",
        active: "Aktif",
        passive: "Pasif",
        edit: "Düzenle",
        addOrUpdateAuthDesc:
          "Bu kullanıcının erişimi için lütfen ilgili modülleri seçiniz",
        edit_authority: "Kullanıcı Yetkisini Düzenle",
        select_department_and_permissions: "Lütfen departman ve yetki seçiniz!",
        department_permissions_assigned:
          "Departman yetkileri başarıyla atandı!",
        select_user_and_permissions: "Lütfen kullanıcı ve yetki seçiniz!",
        user_permissions_assigned: "Kullanıcı yetkileri başarıyla atandı!",
        add_permission: "Yetki Ekle",
        role_permission: "Rol Yetkilendirme",
        department_permission: "Departman Yetkilendirme",
        user_permission: "Kullanıcı Yetkilendirme",
        select_role: "Rol Seçimi",
        select_department: "Departman Seçimi",
        select_user: "Kullanıcı Seçimi",
        assign_permission: "Yetki Ver",
        "topOptions.add": "Ekle",
        "topOptions.filter": "Filtrele",
        "topOptions.add_permission": "Yetki Ekle",
        "detailsFilter-department": "Departman",
        "detailsFilter-user": "Kullanıcı",
        "detailsFilter-permission_name": "Yetki Adı",
        "detailsFilter-status": "Durum",
        "detailsFilter-role": "Rol",
        "detailsFilter-filter": "Filtrele",
      },
      form: {
        transactionSuccessful: "İşlem Başarılı",
        transactionFaild: "İşlem Başarısız",
      },

      pauses: {
        pauses: "Molalar",
      },
      settings: {
        settings: "Ayarlar",
        newPassword: "Yeni Şifre",
        oldPassword: "Eski Şifre",
        changePassword: "Şifre Değiştirme",
        change: "Değiştir",
        profile: "Profil",
        confirmNewPassword: "Yeni Parolayı Onayla",
        passwordsDoNotMatch: "Parolalar eşleşmiyor",
        passwordRequired: "Parola gerekli",
      },
      validation: {
        requiredField: "Bu alan zorunludur",
      },
      roles: {
        roles: "Roller",
        add: "Ekle",
        save: "Kaydet",
        edit: "Düzenle",
        delete: "Sil",
        name: "İsim",
        addRole: "Rol Ekle",
        editRole: "Rolü Düzenle",
        cancel: "Vazgeç",
        warning: "Uyarı",
        deleteModalDesc: "Bu öğe silinecek. Onaylıyor musunuz?",
      },
      fileManager: {
        fileManager: "Dosya Yöneticisi",
        uploadFile: "Dosya Yükle",
        folders: "Dosyalar",
        addFolder: "Klasör Ekle",
        editFolder: "Klasör Güncelle",
        cancel: "Vazgeç",
        delete: "Sil",
        warning: "Uyarı",
        name: "İsim",
        save: "kaydet",
        deleteModalDesc: "Bu öğe silinecek. Onaylıyor musunuz?",
        unSelectFileTitle: "Bir Klasör Seçin",
        unSelectFileDesc: "Dosyaları incelemek için  tıklayınız.",
        uploadFile: "Dosya Yükle",
        downloadFile: "İndir",
        cardView: "Kart Görünümü",
        listView: "Liste Görünümü",
        insertDate: "Oluşturma Tarihi",
        updateDate: "Güncelleme Tarihi",
        type: "Tip",
        fileSize: "Boyut",
        editName: "İsmi Dünzenle",
      },
      profession: {
        professions: "Meslekler",
        add: "Ekle",
        edit: "Düzenle",
        delete: "Sil",
        name: "İsim",
        addProfession: "Meslek Ekle",
        editProfession: "Mesleği Düzenle",
        cancel: "Vazgeç",
        warning: "Uyarı",
        deleteModalDesc: "Bu öğe silinecek. Onaylıyor musunuz?",
      },
      customerSource: {
        customerSources: "Müşteri Kaynakları",
        add: "Ekle",
        edit: "Düzenle",
        delete: "Sil",
        name: "Ad",
        addCustomerSource: "Müşteri Kaynağı Ekle",
        editCustomerSource: "Müşteri Kaynağını Düzenle",
        cancel: "İptal",
        warning: "Uyarı",
        deleteModalDesc: "Bu öğe silinecek. Onaylıyor musunuz?",
      },

      notificationWay: {
        notificationWays: "Bildirim Yolları",
        add: "Ekle",
        edit: "Düzenle",
        delete: "Sil",
        name: "İsim",
        addNotificationWay: "Bildirim Yol Ekle",
        editNotificationWay: "Bildirim Yolu Güncelle",
        cancel: "Vazgeç",
        warning: "Uyarı",
        deleteModalDesc: "Bu öğe silinecek. Onaylıyor musunuz?",
      },
      sector: {
        sectors: "Sektörler",
        add: "Ekle",
        edit: "Düzenle",
        delete: "Sil",
        name: "İsim",
        addSectors: "Sektör Ekle",
        editSectors: "Sektörü Düzenle",
        cancel: "Vazgeç",
        warning: "Uyarı",
        deleteModalDesc: "Bu öğe silinecek. Onaylıyor musunuz?",
      },
      auditLog: {
        logs: "Loglar",
        functionName: "Fonksiyon Adı",
        module: "Modül",
        selectedCount: "Seçilen Sayısı",
        exportedCount: "Dışa Aktarılan Sayısı",
      },
      subjectTicket: {
        subjectTickets: "Çağrı Konuları",
        add: "Ekle",
        edit: "Düzenle",
        delete: "Sil",
        name: "İsim",
        addSubjectTicket: "Konu Ekle",
        editSubjectTicket: "Konuyu Düzenle",
        cancel: "Vazgeç",
        warning: "Uyarı",
        deleteModalDesc: "Bu öğe silinecek. Onaylıyor musunuz?",
      },
      classification: {
        classifications: "Sınıflandırmalar",
        add: "Ekle",
        edit: "Düzenle",
        delete: "Sil",
        name: "Sınıf / Etiket Adı",
        tag: "Etiket",
        addClassification: "Sınıflandırma Ekle",
        editClassification: "Sınıflandırmayı Düzenle",
        cancel: "Vazgeç",
        warning: "Uyarı",
        deleteModalDesc: "Bu öğe silinecek. Onaylıyor musunuz?",
      },
      callNotification: {
        list: {
          warning: "Uyarı",
          ok: "Tamam",
          cancel: "Vazgeç",
          screenWillBeTrunOff: "Ekran kapanacak, onaylıyor musunuz?",
          saveAndClose: "Kaydet ve Kapat",
          closeTakeNote: "Notu Kapat",
          openTakeNote: "Notu Aç",
          addCustomer: "Müşteri Ekle",
          findCustomer: "Müşteri Bul",
          selectCustomer: "Müşteri Seç",
          customerRepresentative: "Müşteri Temsilcileri",
          closeCustomerInfoes: "Müşteri Bilgilerini Kapat",
          openCustomerInfoes: "Müşteri Bilgilerini Aç",
          individual: "Bireysel",
          corporate: "Kurumsal",
          callNote: "Çağrı Notu",
        },
      },

      tempCustomer: {
        list: {
          tempCustomers: "Geçici Müşteriler",
          addButton: "Ekle",
          deleteAllButton: "Tümünü Sil",
          export: "Dışa Aktar",
          import: "İçe Aktar",
          filter: "Filtrele",
          searchPlaceholder: "Ara",
        },
        filter: {
          filterData: "Verileri Filtrele",
          filterButton: "Filtrele",
          fullName: "Ad Soyad",
          identificationOrTaxNumber: "T.C. veya Vergi Numarası",
          filterAddress: "Adrese Göre Filtrele",
        },
        import: {
          importData: "Veri İçe Aktar",
          upload: "Yükle",
          uploaderFileTitle: "Dosyayı Yükleyin veya Sürükleyin",
          uploaderFileDesc: "Desteklenen dosya formatı: xlsx",
        },
      },
      import: {
        selectSheet: "Sayfa Seç",
        sourceName: "Kaynağın Adı",
        excelColumns: "Excel Sütunları",
        modelFields: "Model Alanları",
        save: "Kaydet",
      },
      pause: {
        list: {
          breakRequests: "Mola Talepleri",
          estimatedFinish: "Tahmini Bitiş",
          start: "Başlangıç",
          end: "Bitiş",
          finishBreakTime: " Molayı Bitir",
          counterTimerDesc: "Molanızın bitmesine kalan süre",
          addButton: "Ekle",
          status: "Durum",
          ok: "Tamam",
          cancel: "Vazgeç",
          breakExitTime: "Mola Çıkış Saati",
          sendRequest: "Talep Gönder",
          warning: "Uyarı",
          breakType: "Mola Türü",
          awaitingApproval: "Onay Bekleyenler",
          accepted: "Kabul Edilenler",
          rejected: "Reddedilenler",
          started: "Başlayanlar",
          completed: "Tamamlananlar",
          canceled: "İptal Edilenler",
          cancelRequest: "İptal Talebi",
          startEndTime: "Başlangıç - Bitiş Zamanı",
          seeAllStatuses: "Tüm Durumları Gör",
          staffName: "Personel Adı",
          date: "Tarih",
          descripion: "Açıklama",
          endTime: "Bitiş Süresi",
          startDate: "Başlangıç Tarihi",
          startTime: "Başlangıç Saati",
          allowedMin: "İzin Verilen Dakika",
          approvalStatus: "Onay Durumu",
          staffDesc: "Personel Açıklaması",
          officialStatement: "Yetkili Açıklaması",
          acceptButton: "Kabul Et",
          rejectButton: "Reddet",
          updateBreakStatus: "Mola Durumunu Güncelle",
        },
      },
      pauseType: {
        pauseTypes: "Mola Tipleri",
        add: "Ekle",
        edit: "Düzenle",
        delete: "Sil",
        name: "Ad",
        allowedMin: "İzin Verilen Dakika",
        addPauseType: "Mola Tipi Ekle",
        editPauseType: "Mola Tipini Güncelle",
        cancel: "İptal",
        warning: "Uyarı",
        deleteModalDesc: "Bu öğe silinecek. Onaylıyor musunuz?",
      },

      language: {
        languages: "Diller",
        add: "Ekle",
        edit: "Düzenle",
        delete: "Sil",
        name: "İsim",
        addLanguage: "Dil Ekle",
        editLanguage: "Dili Düzenle",
        cancel: "Vazgeç",
        warning: "Uyarı",
        deleteModalDesc: "Bu öğe silinecek. Onaylıyor musunuz?",
        active: "Aktif",
        passive: "Pasif",
        status: "Durum",
        code: "Kod",
      },
      department: {
        selectedDepartment: "Seçilen Departman",
        departments: "Departmanlar",
        add: "Ekle",
        edit: "Düzenle",
        delete: "Sil",
        name: "İsim",
        users: "Kullanıcılar",
        addUsers: "Kullanıcı Ekle",
        addDepartment: "Departman Ekle",
        editDepartment: "Departman Düzenle",
        cancel: "Vazgeç",
        warning: "Uyarı",
        deleteModalDesc: "Bu öğe silinecek. Onaylıyor musunuz?",
        active: "Aktif",
        passive: "Pasif",
        status: "Durum",
        code: "Kod",
        description: "Açıklama",
      },
      ticket: {
        list: {
          numberDaysPassed: "Geçen Gün Sayısı",
          tickets: "Ticketler",
          explainActionYouPerformed: "Gerçekleştirdiğiniz işlemi açıklayınız",
          type: "Tür",
          subject: "Konu",
          comments: "Yorumlar",
          description: "Açıklama",
          save: "Kaydet",
          insertDate: "Oluşturma Tarihi",
          ticket: "Ticket",
          dateRange: "Tarih Aralığı",
          title: "Başlık",
          subject: "Konu",
          customer: "Müşteri",
          departments: "Departmanlar",
          email: "E-Posta",
          all: "Hepsi",
          notificationWayNone: "Hiç Biri",
          notificationWay: "Bildirim Tercihi",
          priority: "Öncelik",
          endDate: "Bitiş Tarihi",
          uploadFile: "Dosya Yükle",
          email: "E-posta",
          sms: "SMS",
          push: "Push Bildirim",
          all: "Hepsi",
          none: "Hiçbiri",
          low: "Düşük",
          medium: "Orta",
          high: "Yüksek",
          critical: "Kritik",
          description: "Açıklama",
          addTicket: "Ticket Ekle",
          editTicket: "Ticket Düzenle",
          add: "Ekle",
          edit: "Düzenle",
          delete: "Sil",
          name: "İsim",
          cancel: "Vazgeç",
          warning: "Uyarı",
          deleteModalDesc: "Bu öğe silinecek. Onaylıyor musunuz?",
        },
        filter: {
          filterData: "Verileri Filtrele",
          filterButton: "Filtrele",
        },
      },
      task: {
        list: {
          status: "Durum",
          reporterUser: "Bildiren Kişi",
          tasks: "Görevler",
          assignedUser: "Atanan Kullanıcı",
          type: "Tür",
          task: "Görev",
          dateRange: "Tarih Aralığı",
          title: "Başlık",
          departments: "Departmanlar",
          notificationWay: "Bildirim Tercihi",
          priority: "Öncelik",
          endDate: "Bitiş Tarihi",
          email: "E-posta",
          sms: "SMS",
          push: "Push Bildirim",
          all: "Hepsi",
          none: "Hiçbiri",
          low: "Düşük",
          medium: "Orta",
          high: "Yüksek",
          critical: "Kritik",
          description: "Açıklama",
          addTask: "Görev Ekle",
          editTask: "Görev Düzenle",
          add: "Ekle",
          edit: "Düzenle",
          delete: "Sil",
          name: "İsim",
          cancel: "Vazgeç",
          warning: "Uyarı",
          deleteModalDesc: "Bu öğe silinecek. Onaylıyor musunuz?",
        },
        filter: {
          filterData: "Verileri Filtrele",
          filterButton: "Filtrele",
        },
      },
      autoDialer: {
        list: {
          status: "Durum",
          callNotes: "Çağrı Notları",
          pending: "Beklemede",
          description: "Açıklama",
          incomingCall: "Gelen Arama",
          planed: "Planlanan",
          addToArchive: "Arşive Ekle",
          archive: "Arşiv",
          youMustMatchFieldsYourFile:
            "Dosyanızdaki alanları eşleştirmeniz gerekir",
          itemSelected: "öğe seçildi",
          inProgress: "Devam Ediyor",
          completed: "Tamamlandı",
          canceled: "İptal Edildi",
          numberDataAddedSystem: "Sisteme eklenmiş veri sayısı",
          afterSucessImportTempCustoemrAutoDilerDesc:
            "Seç ve devam et tıklayarak otomatık arayıcınızı oluşturabilirsiniz",
          autoDialers: "Otomatik Aramalar",
          save: "Kaydet",
          dateRange: "Tarih Aralığı",
          title: "Başlık",
          start: "Başla",
          cancel: "İptal Et",
          complete: "Tamamla",
          ok: "Tamam",
          numberSelectedCustomers: "Aranacak Müşteri Sayısı",
          startStatusDesc:
            "Otomatik arayıcı başlancacaktır. Onaylıyor musunuz?",
          numberSuccessfulUploads: "Başarılı yükleme sayısı",
          numberitemGivingErrors: "Hata veren öğe sayısı",
          cancelStatusDesc:
            "Otomatik arayıcı iptal edilecektır. Onaylıyor musunuz?",
          completeStatusDesc:
            "Otomatik arayıcı tamamlancaktır. Onaylıyor musunuz?",
          archiveStatusDesc:
            "Otomatik arayıcı arşive eklenecektir. Onaylıyor musunuz?",
          startDate: "Başlangıç Tarihi",
          queueNumber: "Kuyruk Numarası",
          done: "Tamamlanan",
          total: "Toplam",
          ruleName: "Kural Adı",
          queue: "Kuyruk",
          addCallingData: "Arama Verisi Ekle",
          selectAndCountinue: "Seç ve Devam Et",
          importData: "Veri Aktar",
          customers: "Müşteriler",
          externalData: "Harici Veri",
          importDataTitle: "Veri Aktarma Başlığı",
          importDataDesc: "Veri aktarma açıklaması",
          description: "Açıklama",
          addAutoDialer: "Otomatik Arama Ekle",
          editAutoDialer: "Otomatik Arama Düzenle",
          add: "Ekle",
          edit: "Düzenle",
          delete: "Sil",
          name: "İsim",
          cancel: "Vazgeç",
          warning: "Uyarı",
          deleteModalDesc: "Bu öğe silinecek. Onaylıyor musunuz?",
        },
        filter: {
          filterData: "Verileri Filtrele",
          filterButton: "Filtrele",
        },
      },
      quickfilter: {
        deleteSuccess: "Hızlı filtre başarıyla silindi",
        saveSuccess: "Hızlı filtre başarıyla kaydedildi",
        filterLoading: "Hızlı filtreler yükleniyor...",
        filterLoadingError: "Hızlı filtreler yüklenirken hata:",
        filterSaveingError: "Hızlı filtre kaydedirlen hata oluştu",
        filterDeletApprove: "Filtre Silme Onayı",
        filterDeleteText: "filtresi silinecektir.Bu işlemi onaylıyor musunuz?",
        filterApproveText: "Onayla",
        filterCancelText: "İptal",
        filterDeleteText: "Filtreyi Sil",
        delete: "Sil",
        saveFilter: "Hızlı Filtre Kaydet",
        saveText: "Kaydet",
        filterName: "Filtre Adı",
        filterNameRequiredError: "Hızlı filtre adı zorunludur",
      },
      threecxqueues: {
        queues: "3CX Kuyruk",
        queueName: "Kuyruk Adı",
        queueNumber: "Kuyruk Numarası",
        queueAgents: "Temsilciler",
        addQueu: "Kuyruk Ekle",
        updateQueu: "Kuyruk Düzenle",
        delQueu: "Kuyruk Sil",
        cancel: "İptal",
        delete: "Sil",
        deleteModalDesc: "Bu kuyruk silinecek. Onaylıyor musunuz?",
        warning: "Uyarı",
        edit: "Düzenle",
        add: "Ekle",
      },
      notes: {
        note: "Arama Notları",
        noteTitle: "Arama Notları",
        callPhone: "Numara",
        noteDescription: "Arama Açıklaması",
        customerName: "Müşteri",
        insertUser: "Ekleyen Kişi",
        insertDate: "Eklenme Tarihi",
        customerId: "Müşteri",
        insertUserId: "Ekleyen Kişi",
        dateRange: "Tarih Aralığı",
        detail: "Çağrı Notları",
      },
      workFlow: {
        workFlows: "İş Akışları",
        add: "Ekle",
        edit: "Düzenle",
        delete: "Sil",
        name: "İsim",
        description: "Açıklama",
        addWorkFlow: "İş Akışı Ekle",
        editWorkFlow: "İş Akışını Düzenle",
        copyWorkFlow: "İş Akışını Kopyala",
        openEditor: "Editörü Aç",
        addStep: "Adım Ekle",
        editStep: "Adımı Düzenle",
        fromStep:"Başlangıç Adımı",
        toStep:"Hedef Adımı",
        deleteStep: "Adımı Sil",
        copy: "Kopyala",
        newName: "Yeni İsim",
        newDescription: "Yeni Açıklama",
        save: "Kaydet",
        addTransition: "Geçiş Ekle",
        editTransition: "Geçişi Düzenle",
        deleteTransition: "Geçişi Sil",
        addNode: "Düğüm Ekle",
        editNode: "Düğümü Düzenle",
        deleteNode: "Düğümü Sil",
        type: "Tür",
        process: "İşlem",
        start: "Başlangıç",
        end: "Bitiş",
        fromNode: "Başlangıç Düğümü",
        toNode: "Hedef Düğüm",
        addRule: "Kural Ekle",
        editRule: "Kuralı Düzenle",
        back: "Geriye Dön",
        deleteNode: "Düğümü sil",
        deleteTransition: "Geçişi sil",
        saveAndAddRule:"Kaydet ve Kural Ekle",

        tourStep1Title:"Her bir İş Adımı için durum ekleyin",
        tourStep1Desc:"İş Süreçleri otomize etmek için ekipler arası görev geçiş noktaları ekleyin",
        tourStep2Title:"İş Adımlarını Birbirine Bağlayın",
        tourStep2Desc:"Ekibinizin Adımlar Arasında Geçiş Kuralını Ayarlayın",
        tourStep3Title: "Adımlar ve bağlantılar",
        tourStep3Desc: "Oluşturduğunuz adımları ve aralarındaki bağlantıları toplu şekilde inceleyin.",
        tourStep3Title: "Adımlar ve bağlantılar",
        tourStep3Desc: "Oluşturduğunuz adımları ve aralarındaki bağlantıları toplu şekilde inceleyin.",
        tourStep4Title: "Genel Görünüm Haritası",
        tourStep4Desc: "Akış diyagramınızdaki tüm adımları küçük bir harita üzerinden görün. Hızlıca konum değiştirin.",
        tourStep5Title: "Yakınlaştır / Uzaklaştır Kontrolleri",
        tourStep5Desc: "Diyagramı büyütmek veya küçültmek için bu kontrolleri kullanabilirsiniz. Gözlemi kolaylaştırır.",
        tourStep6Title: "Diyagram Önizlemesi",
        tourStep6Desc: "Sayfanın genel yapısını bu alanda görebilirsiniz. Önizleme, hangi adımın nerede olduğunu anlamanızı sağlar.",
        
      },
    },
  },
  EN: {
    translation: {
      clearFilterButton: "Clear All Filters",
      atusageProcessError:
        "This item is in use by other processes and cannot be deleted.",
      welcome: "Welcome",

      login: "Login",
      generalSearch: "Search...",
      team: {
        team: "Team",
      },
      notification: {
        approveChangeAllNotificationStatus:
          "The status of all notifications will change, do you approve?",
        notifications: "Notifications",
        readed: "Read",
        unReaded: "Unread",
        title: "Title",
        message: "Message",
        date: "Date",
        markAllNotificationsRead: "Mark all notifications as read",
      },
      auditLog: {
        logs: "Logs",
        functionName: "Function Name",
        module: "Module",
        selectedCount: "Selected Count",
        exportedCount: "Exported Count",
      },
      recording: {
        recordings: "Recordings",
        caller: "Caller",
        callerName: "",
        callee: "Callee",
        calleeName: "CalleName",
        calleePhoneNumber: "Callee Phone Number",
        callerPhoneNumber: "Caller Phone Number",
        showSummaryCall: "Show Summary Call",
        direction: "Direction",
        inbound: "Inbound",
        outbound: "Outbound",
        startTime: "Start Time",
        endTime: "End Time",
        answeredTime: "Answered Time",
        talkDurationInSeconds: "Talk Duration In Seconds",
        talkDurationInSeconds: "TotalDurationInSeconds",
        copyDownloadLink: "Copy Download Link",
        isAnswered: "Is Answered",
        callStatus: "Call Status",
        extension: "Extensiion",
        transcription: "Transcription",
        export: "Export",
        delete: "Delete",
        missed: "Missed",
        answerd: "Answered",
        summary: "Summary",
        viewDetails: "View Details",
        interviewSummary: "Interview Summary",
        fullContent: "Full Content",
        viewDetails: "Detayları Gör",
        download: "İndir",
        invalidLink: "Invalid Link",
        callDetails: "Call Details",
        sourceParticipantName: "Source Participant Name",
        sourceParticipantPhoneNumber: "Source Participant Phone Number",
      },
      dashboard: {
        dashboard: "Dashboard",
        statusOfTickets: "Status of Tickets",
        priorityStatusTasks: "Priority Status of Tasks",
        statusOfTickets: "Status of Tickets",
        topUsersByTaskCount: "Top User By Task Count",
        searchAssignmentGraph: "Search Assignment Graph",
        low: "Low",
        medium: "Medium",
        high: "High",
        critical: "Critical",
        unassigned: " Un Assigned",
        unAssignedDesc: "Task has not been assigned to anyone yet",
        countinue: "In Progress",
        countinueDesc: "The task is currently in progress",
        done: "Completed",
        doneDesc: "The task has been completed",
        urgent: "Urgent",
        urgentDesc: "The task has been completed",
        urgentDesc: "This is an urgent task",
      },
      general: {
        pageNotFound: "Page Not Found",
        gotoToDashboard: "Go To Dashboard",
        forbiddenDesc: "You Do Not Have Permission To Access This Page",
      },
      account: {
        login: "Login",
        email: "Email",
        password: "Password",
        forgotPassword: "Forgot Password",
        sendEmail: "Send Email",
        emailSent: "Email Sent",
        changePassword: "Change Password",
        newPassword: "New Password",
        inCorrectEmailOrPass: "Your email or password is incorrect",
      },
      header: {
        takeBreak: "Take a Break",
        myBreaks: "My Breaks",
        avaliable: "Available",
        away: "Away",
        doNotDistrub: "Do Not Disturb",
        lunch: "Lunch",
        businessTrip: "Business Trip",
        logout: "Logout",
        settings: "Settings",
      },
      adminSidebar: {
        users: "Users",
        customers: "Customers",
        fileManager: "File Manager",
        profession: "Profession",
        sectors: "Sectors",
        subjectTickets: "Subject Tickets",
        ticket: "Ticket",
        customerSource: "Müşteri kaynağı",
        task: "Task",
        classification: "Classification",
        importData: "Import Data",
        autoDialer: "Auto Dialer",
        workflow: "Workflow",
        pauseManagement: "Pause Management",
        department: "Department",
        recordings: "Recordings",
        authority: "Authority",
        callReports: "Call Reports",
        roles: "Roles",
        report: "Report",
        languages: "Languages",
      },
      users: {
        users: "Users",
        addButton: "Add",
        deleteAllButton: "Delete",
        import: "Import",
        export: "Export",

        searchPlaceholder: "Search",
        list: {
          user: "User",
          email: "Email",
          extension: "Extension",
          department: "Department",

          delete: "Delete",
          edit: "Edit",
          cancel: "Cancel",
          warning: "Warning",
          deleteModalDesc: "This item will be deleted.Confirm?",
        },
        add: {
          general: "General",
          permissions: "Permission",
          options: "Options",
          extension: "Extension",
          name: "Name",
          surName: "Surname",
          email: "Email",
          phone: "Phone",
          password: "Password",
          status: "Status",
          active: "Active",
          pasive: "Passive",
          addUser: "Add User",
          editUser: "Edit User",
          save: "Save",
          extensionNumberFindError: "This number has been registered before",
          passwordDesc: {
            desc1: "At least one lowercase letter is required.",
            desc2: "At least one uppercase letter is required.",
            desc3: "At least one digit is required.",
            desc4: "Password must be at least 6 characters long.",
          },
        },
        import: {
          importData: "Import Data",
          upload: "Upload",
          uploaderFileTitle: "Click Or Drag File",
          uploaderFileDesc: "Supported xlsx file format",
        },
        filter: {
          filterData: "Filter Data",
          filterButton: "Filter",
        },
      },
      customers: {
        customers: "Customers",

        addButton: "Add",
        deleteAllButton: "Delete",
        export: "Export",
        import: "Import",
        filter: "Filter",
        searchPlaceholder: "Search",
        list: {
          customer: "Customer",
          customerPhone: "Customer Phone",
          type: "Type",
          sector: "Sector",
          phone: "Phone",
          classification: "Classification",
          email: "Email",
          extension: "Extension",
          department: "Department",
          delete: "Delete",
          edit: "Update",
          cancel: "Cancel",
          warning: "Warning",
          deleteModalDesc: "This item will be deleted. Do you confirm?",
        },
        add: {
          addressTitle: "Address Title",
          dataSucessfullayUploader: "Your data has been successfully uploaded",
          general: "General",
          inform: "Inform",
          outbound: "Outbound",
          addNote: "Add Note",
          inbound: "Inbound",
          missed: "Missed",
          assignCustomerRepresentative: "Customer Representative Horse",
          addManuallyAddress: "Add Manual Address",
          addAddressWithGoogle: "Add Address with Google",
          customerRepresentative: "Custome rRepresentative",
          classificationDesc:
            "Classification allows you to group your customers based on their behavior, value, or characteristics.",
          classificationDescExample:
            "E.g.: You can create groups like VIP, Frequent Complainer, Blacklist, or High Potential.",
          concat: "Concat",
          permissions: "Permissions",
          options: "Options",
          extension: "Extension",
          name: "Name",
          surName: "Surname",
          email: "Email",
          noneCallStatus: "None",
          talking: "Talking",
          answered: "Answerd",
          missed: "Missed",
          ended: "Ended",
          phone: "Phone",
          customerKind: "Customer Kind",
          customer: "Customer",
          potentialCustomer: "Potential Customer",
          profession: "Profession",
          renew: "Renew",
          active: "Active",
          passive: "Passive",
          suspended: "Suspended",
          customerSource: "Customer Source",
          customerStatus: "Customer Status",
          taxOffice: "Tax Office",
          identification: "Identification",
          mainLanguage: "Main Language",
          availableLanguages: "Available Languages",
          description: "Description",
          individual: "Individual",
          corporate: "Corporate",
          companyName: "Company Name",
          topCompany: "Top Company",
          contact: "Contact",
          title: "Title",
          languages: "Languages",
          save: "Save",
          status: "Status",
          segmentations: "Segmentations",
          addresses: "Addresses",
          country: "Country",
          state: "State",
          address: "Address",
          city: "City",
          neighborhood: "Neighborhood",
          postCode: "Postal Code",
          redirection: "Redirection",
          mananger: "Manager",
          informationalEmails: "Informational Emails",
          call: "Call",
          agent: "Agent",
          durations: "Durations",
          direction: "Direction",
          date: "Date",
          chat: "Chat",
          channel: "Channel",
          users: "Users",
          notes: "Notes",
          createdUser: "Created By",
          createdDate: "Created Date",
          password: "Password",
          status: "Status",
          active: "Active",
          pasive: "Passive",
          addCustomer: "Add Customer",
          editCustomer: "Edit Customer",
          taxNumber: "Tax Number",
          identificationNumber: "Identification Number",
          save: "Save",
          extensionNumberFindError: "This number is already registered",
          passwordDesc: {
            desc1: "Must contain at least one lowercase letter.",
            desc2: "Must contain at least one uppercase letter.",
            desc3: "Must contain at least one number.",
            desc4: "Must be at least 6 characters long.",
          },
        },
        filter: {
          filterData: "Filter Data",
          filterButton: "Filter",
          fullName: "Full Name",
          identificationOrTaxNumber: "Identification or Tax Number",
          filterAddress: "Filter Address",
        },
        import: {
          importData: "Import Data",
          upload: "Upload",
          uploaderFileTitle: "Click or Drag File",
          uploaderFileDesc: "Supported file format: xlsx",
        },
      },
      insertDate: "Oluşturma Tarihi",
      leftMainSidebar: {
        admin: "Admin",
      },
      panel: {
        panel: "Panel",
      },
      pauses: {
        pauses: "Pauses",
      },
      chat: {
        chat: "Chat",
        chats: "Chats",
        next: "Next",
        previous: "Previous",
        countinue: "Countinue",
        startChat: "Start Chat",
        createGroupChat: "Create Group Chat",
        newConversation: "New Conversation",
        start: "Start",
        newConversationInputDesc: "Type name,extension number or email",
        archive: "Archive",
        endChat: "End Chat",
        openTicket: "Open Ticket",
        block: "Block",
        cleanAllHistories: "Clean All Histories",
      },
      calls: {
        calls: "Calls",
      },
      settings: {
        settings: "Settings",

        newPassword: "New Password",
        oldPassword: "Old Password",
        changePassword: "Change Password",
        change: "Change",
        profile: "Profile",
      },
      form: {
        transactionSuccessful: " Transaction Successful",
        transactionFaild: "Transaction Faild",
      },
      authority: {
        authority: "Authority",
        addOrUpdateAuthDesc:
          "Please select the relevant modules for this user to access",
        user_name: "User Name",
        department: "Department",
        role: "Role",
        authority: "Yetki",
        selectAll: "Select All",
        removeSelectAll: "Tüm seçilileri kaldır",
        role: "Rol",
        save: "Save",
        email: "Email",
        status: "Status",
        authority: "Authority",
        role: "Role",
        save: "Save",
        active: "Active",
        passive: "Passive",
        edit: "Edit",
        edit_authority: "Edit User Authority",
        select_department_and_permissions:
          "Please select department and permissions!",
        department_permissions_assigned:
          "Department permissions successfully assigned!",
        select_user_and_permissions: "Please select user and permissions!",
        user_permissions_assigned:
          "User permissions have been assigned successfully!",
        add_permission: "Add Authorisation",
        role_permission: "Role Authorisation",
        department_permission: "Department Authorisation",
        user_permission: "User Authorisation",
        select_role: "Select Role",
        select_department: "Select Department",
        select_user: "User Selection",
        assign_permission: "Authorise",
        "topOptions.add": "Add",
        "topOptions.filter": "Filter",
        "topOptions.add_permission": "Add Authority",
        "detailsFilter-department": "Department",
        "detailsFilter-user": "User",
        "detailsFilter-permission_name": "Permission Name",
        "detailsFilter-status": "Status",
        "detailsFilter-role": "Role",
        "detailsFilter-filter": "Filter",
      },
      task: {
        task_main_title: "Task",
        customer: "Customer",
        assignedUser: "Assigned User",
        customer_placeholder: "Enter Customer",
        additional_phone: "Additional Phone",
        title_notification: "Title / Notification Way",
        title_placeholder: "Enter Title",
        notification_way_placeholder: "Select Notification Way",
        description: "Description",
        description_placeholder: "Enter Description",
        user: "User",
        assigned_user: "Assigned User",
        reporter_user: "Reporter User",
        department_user: "Department / User",
        department: "Department",
        type_phone: "Type / Phone",
        type_placeholder: "Select Type",
        ariza_satis_iptal_iade: "Ariza, Satiş, İptal, İade",
        satis: "Satiş",
        iptal: "İptal",
        iade: "İade",
        click_to_upload: "Click to Upload",
        code_customer: "Code / Customer",
        customer_placeholder: "Enter Customer",
        "task-add-button": "Add",
        "task-filter-button": "Filter",
        "task-add-new-task": "Add Task",
        task_title: "Title",
        reporter: "Reporter",
        assigned_to: "Assigned To",
        department: "Department",
        start_date: "Start Date",
        end_date: "End Date",
        customer: "Customer",
        tag: "Tag",
        task_type: "Task Type",
        status: "Status",
        priority: "Priority",
        filter: "Filter",
        status: "Status",
        status_pending: "Pending",
        status_medium: "Medium",
        status_completed: "Completed",
        title: "Title",
        assigned: "Assigned User",
        department: "Department",
        start_date: "Start Date",
        passing_time: "Passing Time",
        due_date: "Due Date",
        ticket: "Ticket",
        priority: "Priority",
        priority_high: "High",
        priority_medium: "Medium",
        priority_low: "Low",
        task_type: "Task Type",
        description: "Description",
        edit: "Edit",
        edit_task: "Edit Task",
      },

      language: {
        languages: "Languages",
        add: "Add",
        edit: "Edit",
        delete: "Delete",
        name: "Name",
        addLanguage: "Add Language",
        editLanguage: "Edit Language",
        cancel: "Cancel",
        warning: "Warning",
        deleteModalDesc: "This item will be deleted.Confirm?",
        active: "Active",
        passive: "Passive",
        status: "Status",
        code: "Code",
      },
      profession: {
        professions: "Professions",
        add: "Add",
        edit: "Edit",
        delete: "Delete",
        name: "Name",
        addProfession: "Add Profession",
        editProfession: "Edit Profession",
        cancel: "Cancel",
        warning: "Warning",
        deleteModalDesc: "This item will be deleted. Do you confirm?",
      },
      customerSource: {
        customerSources: "Customer Sources",
        add: "Add",
        edit: "Edit",
        delete: "Delete",
        name: "Name",
        addCustomerSource: "Add Customer Source",
        editCustomerSource: "Edit Customer Source",
        cancel: "Cancel",
        warning: "Warning",
        deleteModalDesc: "This item will be deleted. Do you confirm?",
      },
      notificationWay: {
        notificationWays: "Notification Ways",
        add: "Add",
        edit: "Edit",
        delete: "Delete",
        name: "İsim",
        addNotificationWay: "Add Notification Way",
        editNotificationWay: "Edit Notification Way",
        cancel: "Cancel",
        warning: "Warning",
        deleteModalDesc: "This item will be deleted. Do you confirm?",
      },

      sector: {
        sectors: "Sectors",
        add: "Add",
        edit: "Edit",
        delete: "Delete",
        name: "Name",
        addSectors: "Add Sector",
        editSectors: "Edit Sector",
        cancel: "Cancel",
        warning: "Warning",
        deleteModalDesc: "This item will be deleted. Do you confirm?",
      },
      fileManager: {
        fileManager: "File Manager",
        uploadFile: "Upload File",
        folders: "Folders",
        addFolder: "Add Folder",
        editFolder: "Edit Folder",
        cancel: "Cancel",
        delete: "Delete",
        warning: "Warning",
        name: "Name",
        save: "Save",
        deleteModalDesc: "This item will be deleted. Do you confirm?",
        unSelectFileTitle: "Select a Folder",
        unSelectFileDesc: "Click to browse files.",
        downloadFile: "Download",
        cardView: "Card View",
        listView: "List View",
      },

      department: {
        departments: "Departments",
        selectedDepartment: "Selected Department",
        add: "Add",
        edit: "Edit",
        delete: "Delete",
        name: "Name",
        topDepartment: "Top Department",
        users: "Users",
        addUsers: "Add Users",
        addDepartment: "Add Department",
        topDepartment: "Üst Departman",
        editDepartment: "Edit Department",
        cancel: "Cancel",
        warning: "Warning",
        deleteModalDesc: "This item will be deleted. Do you confirm?",
        active: "Active",
        passive: "Passive",
        status: "Status",
        code: "Code",
        description: "Description",
      },

      subjectTicket: {
        subjectTickets: "Subject Tickets",
        add: "Add",
        edit: "Edit",
        delete: "Delete",
        name: "Name",
        addSubjectTicket: "Add Subject",
        editSubjectTicket: "Edit Subject",
        cancel: "Cancel",
        warning: "Warning",
        deleteModalDesc: "This item will be deleted. Do you confirm?",
      },
      classification: {
        classifications: "Classifications",
        add: "Add",
        edit: "Edit",
        delete: "Delete",
        name: "Class/Tag Name",
        tag: "Tag",
        addClassification: "Add Classification",
        editClassification: "Edit Classification",
        cancel: "Cancel",
        warning: "Warning",
        deleteModalDesc: "This item will be deleted. Do you confirm?",
      },
      tempCustomer: {
        list: {
          tempCustomers: "TempCustomers",
          addButton: "Add",
          deleteAllButton: "Delete",
          export: "Export",
          import: "Import",
          filter: "Filter",
          searchPlaceholder: "Search",
        },
        filter: {
          filterData: "Filter Data",
          filterButton: "Filter",
          fullName: "Full Name",
          identificationOrTaxNumber: "Identification or Tax Number",
          filterAddress: "Filter Address",
        },
        import: {
          importData: "Import Data",
          upload: "Upload",
          uploaderFileTitle: "Click or Drag File",
          uploaderFileDesc: "Supported file format: xlsx",
        },
      },
      import: {
        selectSheet: "Select Sheet",
        sourceName: "Source Name",
        excelColumns: "Excel Columns",
        modelFields: "Model Fields",
        save: "Save",
      },
      pause: {
        list: {
          breakRequests: "Break Requests",
          estimatedFinish: "Estimated Finish Date",
          status: "Status",
          start: "Start",
          end: "End",
          ok: "Ok",
          cancel: "Cancel",
          breakExitTime: "Break Exit Time",
          descripion: "Description",
          endTime: "End Time",
          sendRequest: "Send Requst",
          warning: "Warning",
          addButton: "Add",
          breakType: "Break Type",
          awaitingApproval: "Awaiting Approval",
          accepted: "Accepted",
          rejected: "Rejected",
          started: "Started",
          completed: "Completed",
          canceled: "Canceled",
          seeAllStatuses: "See All Statuses",
          staffName: "Staff Name",
          date: "Date",
          startDate: "Start Date",
          startTime: "Start Time",
          allowedMin: "Allowed Minutes",
          approvalStatus: "Approval Status",
          staffDesc: "Staff Description",
          officialStatement: "Official Statement",
          cancelRequest: "Cancel Request",
          startEndTime: "Start-End Time",
          acceptButton: "Accept",
          rejectButton: "Reject",
          updateBreakStatus: "Update Break Status",
        },
      },
      pauseType: {
        pauseTypes: "Pause Types",
        add: "Add",
        edit: "Edit",
        delete: "Delete",
        name: "Name",
        allowedMin: "Allowed Minutes",
        addPauseType: "Add Pause Type",
        editPauseType: "Update Pause Type",
        cancel: "Cancel",
        warning: "Warning",
        deleteModalDesc: "This item will be deleted. Do you confirm?",
      },
      callNotification: {
        list: {
          warning: "Warning",
          ok: "OK",
          cancel: "Cancel",
          screenWillBeTrunOff: "The screen will be closed, Do you confirm?",
          saveAndClose: "Save and Close",
          closeTakeNote: "Close Note",
          openTakeNote: "Open Note",
          addCustomer: "Add Customer",
          findCustomer: "Find Customer",
          selectCustomer: "Select Customer",
          customerRepresentative: "Customer Representative",
          closeCustomerInfoes: "Close Customer Info",
          openCustomerInfoes: "Open Customer Info",
          individual: "Individual",
          corporate: "Corporate",
          callNote: "Call Note",
        },
        filter: {
          filterData: "Filter Data",
          filterButton: "Filter",
        },
      },

      roles: {
        roles: "Roles",
        add: "Add",
        save: "Save",
        edit: "Edit",
        delete: "Delete",
        name: "Name",
        addRole: "Add Role",
        editRole: "Edit Role",
        cancel: "Cancel",
        warning: "Warning",
        deleteModalDesc: "This item will be deleted.Confirm?",
      },
      ticket: {
        list: {
          tickets: "Tickets",
          type: "Type",
          ticket: "Ticket",
          explainActionYouPerformed: "Explain the action you performed",
          subject: "Subject",
          comments: "Comments",
          description: "Descripton",
          save: "Save",
          insertDate: "Insert Date",
          numberDaysPassed: "Number of Days Passed",
          dateRange: "Date Range",
          email: "Email",
          all: "All",
          notificationWayNone: "None",
          title: "Title",
          subject: "Subject",
          customer: "Customer",
          departments: "Departments",
          notificationWay: "Notification Preference",
          priority: "Priority",
          endDate: "End Date",
          uploadFile: "Upload File",
          email: "Email",
          sms: "SMS",
          push: "Push Notification",
          all: "All",
          none: "None",
          low: "Low",
          medium: "Medium",
          high: "High",
          critical: "Critical",
          description: "Description",
          addTicket: "Add Ticket",
          editTicket: "Edit Ticket",
          add: "Add",
          edit: "Edit",
          delete: "Delete",
          name: "Name",
          cancel: "Cancel",
          warning: "Warning",
          deleteModalDesc: "This item will be deleted. Do you confirm?",
        },
        filter: {
          filterData: "Filter Data",
          filterButton: "Filter",
        },
      },
      task: {
        list: {
          status: "Status",
          reporterUser: "Reporter User",
          tasks: "Tasks",
          assignedUser: "Assigned User",
          type: "Type",
          task: "Task",
          dateRange: "Date Range",
          title: "Title",
          departments: "Departments",
          notificationWay: "Notification Preference",
          priority: "Priority",
          endDate: "End Date",
          email: "Email",
          sms: "SMS",
          push: "Push Notification",
          all: "All",
          none: "None",
          low: "Low",
          medium: "Medium",
          high: "High",
          critical: "Critical",
          description: "Description",
          addTask: "Add Task",
          editTask: "Edit Task",
          add: "Add",
          edit: "Edit",
          delete: "Delete",
          name: "Name",
          cancel: "Cancel",
          warning: "Warning",
          deleteModalDesc: "This item will be deleted. Do you confirm?",
        },
        filter: {
          filterData: "Filter Data",
          filterButton: "Filter",
        },
      },
      pause: {
        list: {
          breakRequests: "Break Requests",
          finishBreakTime: "End Break Time",
          counterTimerDesc: "Time left until your break ends",
          breakExitTime: "Break Exit Time",
          sendRequest: "Send Request",
          addButton: "Add",
          breakType: "Break Type",
          awaitingApproval: "",
          accepted: "Accepted",
          rejected: "Rejected",
          started: "Started",
          completed: "Completed",
          canceled: "Canceled",
          seeAllStatuses: "See All Statuses",
          staffName: "",
          date: "",
          startDate: "",
          startTime: "",
          allowedMin: "Allowed Min",
          approvalStatus: "Approval Status",
          staffDesc: "",
          officialStatement: "Official Statement",
          acceptButton: "Accept",
          rejectButton: "Reject",
          updateBreakStatus: "",
        },
      },

      autoDialer: {
        list: {
          status: "Status",
          save: "Save",
          description: "Description",
          planed: "Planed",
          archive: "Archive",
          addToArchive: "Add To Archive",
          numberSuccessfulUploads: "Number of successful uploads",
          incomingCall: "Incoming Call",
          numberitemGivingErrors: "Number of items with errors",
          itemSelected: "item selected",
          autoDialers: "Auto Dialers",
          youMustMatchFieldsYourFile:
            "You need to match the fields in your file",
          numberDataAddedSystem: "Number of data added to the system",
          afterSucessImportTempCustoemrAutoDilerDesc:
            "You can create your auto dialer ​​by selecting Select and continue",
          pending: "Pending",
          callNotes: "Call Notes",
          inProgress: "In Progress",
          completed: "Completed",
          canceled: "Canceled",
          dateRange: "Date Range",
          title: "Title",
          startDate: "Start Date",
          numberSelectedCustomers: "Number of Selected Customers",
          queueNumber: "Queue Number",
          done: "Done",
          total: "Total",
          ruleName: "Rule Name",
          queue: "Queue",
          start: "Start",
          cancel: "Cancel",
          ok: "OK",
          complete: "Complete",
          startStatusDesc: "This item will be started. Do you confirm?",
          cancelStatusDesc: "This item will be cancelled. Do you confirm?",
          completeStatusDesc: "This item will be completed. Do you confirm?",
          archiveStatusDesc: "This item will be archived. Do you confirm?",
          addCallingData: "Add Calling Data",
          selectAndCountinue: "Select and Continue",
          importData: "Import Data",
          customers: "Customers",
          externalData: "External Data",
          importDataTitle: "Import Data Title",
          importDataDesc: "Import Data Description",
          description: "Description",
          addAutoDialer: "Add Auto Dialer",
          editAutoDialer: "Edit Auto Dialer",
          add: "Add",
          edit: "Edit",
          delete: "Delete",
          name: "Name",
          cancel: "Cancel",
          warning: "Warning",
          deleteModalDesc: "This item will be deleted. Do you confirm?",
        },
        filter: {
          filterData: "Filter Data",
          filterButton: "Filter",
        },
      },
      quickfilter: {
        deleteSuccess: "Hızlı filtre başarıyla silindi",
        saveSuccess: "Hızlı filtre başarıyla kaydedildi",
        filterLoading: "Hızlı filtreler yükleniyor...",
        filterLoadingError: "Hızlı filtreler yüklenirken hata:",
        filterSaveingError: "Hızlı filtre kaydedirlen hata oluştu",
        filterDeletApprove: "Filtre Silme Onayı",
        filterDeleteText: "filtresi silinecektir.Bu işlemi onaylıyor musunuz?",
        filterApproveText: "Onayla",
        filterCancelText: "İptal",
        filterDeleteText: "Filtreyi Sil",
        delete: "Sil",
        saveFilter: "Hızlı Filtre Kaydet",
        saveText: "Kaydet",
        filterName: "Filtre Adı",
        filterNameRequiredError: "Hızlı filtre adı zorunludur",
      },
      threecxqueues: {
        queues: "3CX Kuyruk",
        queueName: "Kuyruk Adı",
        queueNumber: "Kuyruk Numarası",
        queueAgents: "Temsilciler",
        addQueu: "Kuyruk Ekle",
        updateQueu: "Kuyruk Düzenle",
        delQueu: "Kuyruk Sil",
        cancel: "İptal",
        delete: "Sil",
        deleteModalDesc: "Bu kuyruk silinecek. Onaylıyor musunuz?",
        warning: "Uyarı",
        edit: "Düzenle",
        add: "Ekle",
      },
      workFlow: {
        workFlows: "Workflows",
        add: "Add",
        edit: "Edit",
        delete: "Delete",
        name: "Name",
        description: "Description",
        addWorkFlow: "Add Workflow",
        editWorkFlow: "Edit Workflow",
        copyWorkFlow: "Copy Workflow",
        openEditor: "Open Editor",
        copy: "Copy",
        newName: "New Name",
        newDescription: "New Description",
        save: "Save",
        addTransition: "Add Transition",
        editTransition: "Edit Transition",
        deleteTransition: "Delete Transition",
        addNode: "Add Node",
        editNode: "Edit Node",
        deleteNode: "Delete Node",
        addStep: "Add Step",
        editStep: "Edit Step",
        deleteStep: "Delete Step",
        fromStep:"From Step",
        toStep:"To Step",
        type: "Type",
        process: "Process",
        saveAndAddRule:"Save And Add Rule",
        start: "Start",
        end: "End",
        fromNode: "From Node",
        toNode: "To Node",
        addRule: "Add Rule",
        editRule: "Edit Rule",
        back: "Back",
        deleteNode: "Delete Node",
        deleteTransition: "Delete Transition",
        tourStep1Title: "Add Status for Each Workflow Step",
tourStep1Desc: "Add transition points between teams to automate business processes.",

tourStep2Title: "Connect Workflow Steps",
tourStep2Desc: "Define how your team transitions between steps.",

tourStep3Title: "Steps and Connections",
tourStep3Desc: "Review all the steps and the connections between them at a glance.",

tourStep4Title: "Overview Mini Map",
tourStep4Desc: "View all steps in your flowchart from the mini map. Navigate quickly across the canvas.",

tourStep5Title: "Zoom Controls",
tourStep5Desc: "Use these controls to zoom in and out of the diagram for better visibility.",

tourStep6Title: "Diagram Preview",
tourStep6Desc: "See a preview of the overall layout. Understand where each step is positioned.",

      },
    },
  },
};

const langResource = storedResource
  ? JSON.parse(storedResource)
  : {
      appVersion,
      resources: defaultResources,
    };

i18n.use(initReactI18next).init({
  resources: langResource.resources,
  lng: lang,
  fallbackLng: ["TR", "EN"],
  interpolation: {
    escapeValue: false,
  },
});

export default i18n;
