using System.Reflection;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.OData;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Requests.Application.Abstractions;
using Requests.Application.Rules.Engine;
using Requests.Application.Rules.Engine.Handlers;
using Requests.Infrastructure.Data;
using Requests.Infrastructure.Services;
using Shared.Endpoints;

namespace Requests;

public static class ModuleInitializer
{
    public static IServiceCollection AddRequestsModule(this IServiceCollection services, IConfiguration configuration)
    {
        var connectionString = configuration.GetConnectionString("ApplicationConnection");
        var assembly = Assembly.GetExecutingAssembly();
        services.AddDbContext<RequestsDbContext>(options =>
        {
            options.UseSqlServer(connectionString, b =>
            {
                b.MigrationsHistoryTable(HistoryRepository.DefaultTableName, "Requests");
                b.MigrationsAssembly(assembly);
                b.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery);
            });
            options.EnableSensitiveDataLogging();
        });
        services.AddScoped<IRequestsDbContext>(provider => provider.GetRequiredService<RequestsDbContext>());

        // Rule Engine Services
        services.AddScoped<IRuleEngine, RuleEngine>();
        services.AddScoped<AuthorizationRuleHandler>();
        services.AddScoped<FieldValidationRuleHandler>();
        services.AddScoped<FieldModificationRuleHandler>();
        services.AddScoped<NotificationRuleHandler>();

        services.AddEndpoints(assembly);
        services.AddMediatR(config => config.RegisterServicesFromAssembly(assembly));
        services.AddValidatorsFromAssembly(assembly, includeInternalTypes: true);
        services.AddControllers().AddOData(options => options.Select().Filter().OrderBy().Expand().Count().SetMaxTop(100));

        return services;
    }

    public static IApplicationBuilder UseRequestsModule(this IApplicationBuilder app)
    {
        app.UseEndpoints(endpoints =>
        {
            endpoints.MapControllers();
        });
        return app;
    }
}
