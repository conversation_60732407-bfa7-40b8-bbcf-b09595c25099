using MediatR;
using Requests.Application.Rules.Engine.Handlers;
using Requests.Domain.Enums;
using Shared.Application;

namespace Requests.Application.Rules.GetRuleTypes;

public class GetRuleTypesQueryHandler : IRequestHandler<GetRuleTypesQuery, Result<List<RuleTypeDto>>>
{
    public async Task<Result<List<RuleTypeDto>>> Handle(GetRuleTypesQuery request, CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        var ruleTypes = new List<RuleTypeDto>
        {
            new(
                Id: (int)RuleType.Authorization,
                Name: nameof(RuleType.Authorization),
                Tag: "Validation",
                DisplayName: "Yetkilendirme Kuralı",
                Description: "Bu geçişi yapabilecek kullanıcıları ve rolleri belirler",
                FormConfigurationJson: AuthorizationRuleHandler.FormConfigurationJson
            ),
            new(
                Id: (int)RuleType.FieldValidation,
                Name: nameof(RuleType.FieldValidation),
                Tag: "Validation",
                DisplayName: "Alan Doğrulama Kuralı",
                Description: "Geçiş öncesi ticket alanlarını doğrular",
                FormConfigurationJson: FieldValidationRuleHandler.FormConfigurationJson
            ),
            new(
                Id: (int)RuleType.FieldModification,
                Name: nameof(RuleType.FieldModification),
                Tag: "Action",
                DisplayName: "Alan Değiştirme Kuralı",
                Description: "Geçiş sırasında ticket alanlarını otomatik olarak değiştirir",
                FormConfigurationJson: FieldModificationRuleHandler.FormConfigurationJson
            ),
            new(
                Id: (int)RuleType.Notification,
                Name: nameof(RuleType.Notification),
                Tag: "Action",
                DisplayName: "Bildirim Kuralı",
                Description: "Geçiş sırasında otomatik bildirim gönderir",
                FormConfigurationJson: NotificationRuleHandler.FormConfigurationJson
            )
        };

        return Result<List<RuleTypeDto>>.Success(ruleTypes);
    }
}
