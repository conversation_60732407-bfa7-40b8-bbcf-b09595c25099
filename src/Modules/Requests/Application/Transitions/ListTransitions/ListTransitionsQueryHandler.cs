using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Shared.Application;

namespace Requests.Application.Transitions.ListTransitions;

public class ListTransitionsQueryHandler(IRequestsDbContext context)
    : IRequestHandler<ListTransitionsQuery, PagedResult<TransitionDto>>
{
    public async Task<PagedResult<TransitionDto>> Handle(ListTransitionsQuery request, CancellationToken cancellationToken)
    {
        var query = context.Transitions
            .Include(t => t.FromNode)
            .Include(t => t.ToNode)
            .AsQueryable();

        if (request.FromNodeId.HasValue)
        {
            query = query.Where(t => t.FromNodeId == request.FromNodeId.Value);
        }

        if (request.ToNodeId.HasValue)
        {
            query = query.Where(t => t.ToNodeId == request.ToNodeId.Value);
        }

        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            query = query.Where(t => t.Name.Contains(request.SearchTerm) ||
                                   t.FromNode.Name.Contains(request.SearchTerm) ||
                                   t.ToNode.Name.Contains(request.SearchTerm));
        }

        var totalCount = await query.CountAsync(cancellationToken);

        var transitions = await query
            .OrderBy(t => t.FromNode.Name)
            .ThenBy(t => t.ToNode.Name)
            .ThenBy(t => t.Name)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(t => new TransitionDto
            {
                Id = t.Id,
                FromNodeId = t.FromNodeId,
                ToNodeId = t.ToNodeId,
                FromNodeName = t.FromNode.Name,
                ToNodeName = t.ToNode.Name,
                Name = t.Name,
                RuleCount = t.Rules.Count,
                InsertDate = t.InsertDate,
                UpdateDate = t.UpdateDate
            })
            .ToListAsync(cancellationToken);

        var pagedResult = new PagedResult<TransitionDto>(transitions)
        {
            PageNumber = request.PageNumber,
            PageSize = request.PageSize,
            Count = totalCount,
            FilteredCount = totalCount
        };

        return pagedResult;
    }
}
