using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Shared.Application;
using Shared.Contracts;

namespace Requests.Application.Tickets.GetTicketHistory;

public class GetTicketHistoryQueryHandler(
    IRequestsDbContext dbContext,
    ISharedUserService userService
) : IRequestHandler<GetTicketHistoryQuery, Result<List<TicketHistoryDto>>>
{
    private readonly IRequestsDbContext _dbContext = dbContext;
    private readonly ISharedUserService _userService = userService;

    public async Task<Result<List<TicketHistoryDto>>> Handle(GetTicketHistoryQuery request, CancellationToken cancellationToken)
    {
        // Ticket'ın varlığını kontrol et
        var ticketExists = await _dbContext.Tickets
            .AnyAsync(t => t.Id == request.TicketId, cancellationToken);

        if (!ticketExists)
        {
            return Result.Failure<List<TicketHistoryDto>>("Ticket.NotFound", "Ticket bulunamadı.");
        }

        // History kayıtlarını getir
        var historyRecords = await _dbContext.TicketHistories
            .Where(h => h.TicketId == request.TicketId)
            .OrderByDescending(h => h.InsertDate)
            .ToListAsync(cancellationToken);

        // User bilgilerini getir
        var userIds = historyRecords
            .Where(h => h.InsertUserId.HasValue)
            .Select(h => h.InsertUserId!.Value)
            .Distinct()
            .ToList();

        var users = new Dictionary<Guid, string>();
        if (userIds.Any())
        {
            var usersResult = await _userService.GetUsersByIdsAsync(userIds);
            if (usersResult != null && usersResult.Any())
            {
                users = usersResult.ToDictionary(
                    u => u.Id,
                    u => $"{u.Name} {u.Surname}"
                );
            }
        }

        // DTO'ya dönüştür
        var historyDtos = historyRecords.Select(h => new TicketHistoryDto
        {
            Id = h.Id,
            TicketId = h.TicketId,
            FieldName = h.FieldName,
            OldValue = h.OldValue,
            NewValue = h.NewValue,
            ChangeType = h.ChangeType,
            Description = h.Description,
            ChangeDate = h.InsertDate,
            ChangedByUserId = h.ChangedByUserId ?? h.InsertUserId,
            ChangedByUserName = !string.IsNullOrEmpty(h.ChangedByUserName)
                ? h.ChangedByUserName
                : (h.InsertUserId.HasValue && users.ContainsKey(h.InsertUserId.Value)
                    ? users[h.InsertUserId.Value]
                    : "Sistem")
        }).ToList();

        return Result.Success(historyDtos);
    }
}
