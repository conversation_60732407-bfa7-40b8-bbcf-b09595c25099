using MediatR;
using Requests.Domain;
using Shared.Application;

namespace Requests.Application.Tickets.GetTicketHistory;

public record GetTicketHistoryQuery(Guid TicketId) : IRequest<Result<List<TicketHistoryDto>>>;

public record TicketHistoryDto
{
    public Guid Id { get; init; }
    public Guid TicketId { get; init; }
    public string FieldName { get; init; } = string.Empty;
    public string? OldValue { get; init; }
    public string? NewValue { get; init; }
    public TicketHistoryChangeType ChangeType { get; init; }
    public string? Description { get; init; }
    public DateTime ChangeDate { get; init; }
    public Guid? ChangedByUserId { get; init; }
    public string? ChangedByUserName { get; init; }
}
