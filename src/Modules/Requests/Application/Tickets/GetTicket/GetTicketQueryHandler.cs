using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Shared.Application;
using Shared.Contracts;

namespace Requests.Application.Tickets.GetTicket;

public class GetTicketQueryHandler(
    IRequestsDbContext dbContext,
    ISharedUserService userService,
    ISharedCustomerService customerService
) : IRequestHandler<GetTicketQuery, Result<TicketDto>>
{
    private readonly IRequestsDbContext _dbContext = dbContext;
    private readonly ISharedUserService _userService = userService;
    private readonly ISharedCustomerService _customerService = customerService;

    public async Task<Result<TicketDto>> Handle(GetTicketQuery request, CancellationToken cancellationToken)
    {
        var ticket = await _dbContext.Tickets
            .Include(t => t.TicketFiles)
            .Include(t => t.Status)
            .FirstOrDefaultAsync(t => t.Id == request.Id, cancellationToken);

        if (ticket == null)
        {
            return Result.Failure<TicketDto>("Ticket.NotFound", "Ticket bulunamadı.");
        }

        var subject = await _dbContext.TicketSubjects
            .FirstOrDefaultAsync(s => s.Id == ticket.SubjectId, cancellationToken);

        var comments = await _dbContext.TicketComments
            .Where(c => c.TicketId == ticket.Id)
            .OrderByDescending(c => c.InsertDate)
            .ToListAsync(cancellationToken);

        // Kullanıcı ve müşteri bilgilerini al
        var userTask = await (ticket.UserId.HasValue
            ? _userService.GetUserAsync(ticket.UserId.Value)
            : Task.FromResult<Result<SharedUserDTO>>(null));

        var reporterUserTask = await _userService.GetUserAsync(ticket.ReporterUserId);
        var customerTask = await _customerService.GetCustomerAsync(ticket.CustomerId);
        var notificationWay = await _customerService.GetNotificationWaysAsync();

        var user = userTask?.IsSuccess == true ? userTask.Value : null;
        var reporterUser = reporterUserTask?.IsSuccess == true ? reporterUserTask.Value : null;
        var customer = customerTask?.IsSuccess == true ? customerTask.Value : null;

        var result = new TicketDto
        {
            Id = ticket.Id,
            Code = ticket.Code,
            TicketType = ticket.TicketType,
            TopTicketId = ticket.TopTicketId,
            SubjectId = ticket.SubjectId,
            SubjectName = subject?.Name,
            CustomerId = ticket.CustomerId,
            CustomerName = customer != null ? $"{customer.Name} {customer.Surname}" : null,
            Title = ticket.Title,
            Description = ticket.Description,
            NotificationWayId = ticket.NotificationWayId,
            NotificationWay = notificationWay.Value.FirstOrDefault(x => x.Id == ticket.NotificationWayId)?.Name,
            UserId = ticket.UserId,
            UserName = user != null ? $"{user.Name} {user.Surname}" : null,
            ReporterUserId = ticket.ReporterUserId,
            ReporterUserName = reporterUser != null ? $"{reporterUser.Name} {reporterUser.Surname}" : null,
            Departments = [.. ticket.TicketDepartment.Select(td => new TicketDepartmentDto(
                td.DepartmentId,
                td.DepartmentName
            ))],
            TicketFiles = ticket.TicketFiles?.Select(f => new TicketFileDto
            {
                Id = f.Id,
                TicketId = f.TicketId,
                FileId = f.FileId,
                FileName = f.FileName,
                FilePath = f.FilePath
            }).ToList() ?? [],
            Priority = ticket.Priority,
            StatusId = ticket.StatusId,
            StatusName = ticket.Status?.Name,
            EndDate = ticket.EndDate,
            Watchlist = ticket.Watchlist,
            Tags = ticket.Tags,
            InsertDate = ticket.InsertDate,
            UpdateDate = ticket.UpdateDate,
            Comments = comments?.Select(c => new TicketCommentDto
            {
                Id = c.Id,
                UserId = c.UserId,
                Comment = c.Comment,
                InsertDate = c.InsertDate
            }).ToList()
        };

        return Result.Success(result);
    }
}
