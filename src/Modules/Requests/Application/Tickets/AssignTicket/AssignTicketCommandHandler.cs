using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Shared.Application;
using Shared.Contracts;

namespace Requests.Application.Tickets.AssignTicket;

public class AssignTicketCommandHandler : IRequestHandler<AssignTicketCommand, Result>
{
    private readonly IRequestsDbContext _dbContext;
    private readonly ISharedUserService _userService;
    private readonly ITicketHistoryService _historyService;

    public AssignTicketCommandHandler(
        IRequestsDbContext dbContext,
        ISharedUserService userService,
        ITicketHistoryService historyService)
    {
        _dbContext = dbContext;
        _userService = userService;
        _historyService = historyService;
    }

    public async Task<Result> Handle(AssignTicketCommand request, CancellationToken cancellationToken)
    {
        // Ticket'ı kontrol et
        var ticket = await _dbContext.Tickets
            .FirstOrDefaultAsync(t => t.Id == request.TicketId, cancellationToken);

        if (ticket == null)
        {
            return Result.Failure("Ticket.NotFound", "Ticket bulunamadı.");
        }

        // Kullanıcıyı kontrol et
        var userResult = await _userService.GetUserAsync(request.UserId);
        if (!userResult.IsSuccess)
        {
            return Result.Failure("User.NotFound", "Kullanıcı bulunamadı.");
        }

        // Eski atama bilgisini sakla
        var oldUserId = ticket.UserId;

        // Ticket'ı kullanıcıya ata
        ticket.UserId = request.UserId;

        // Eğer kullanıcı watchlist'te yoksa ekle
        if (!ticket.Watchlist.Contains(request.UserId))
        {
            ticket.Watchlist.Add(request.UserId);
        }

        // History tracking
        await _historyService.TrackTicketAssignedAsync(ticket.Id, oldUserId, request.UserId, cancellationToken);

        await _dbContext.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
