using MediatR;
using Requests.Application.Abstractions;
using Requests.Domain;
using Shared.Application;
using Shared.Contracts;
using Shared.Domain;
using Shared.Utilities;

namespace Requests.Application.Tickets.CreateTicket;

public class CreateTicketCommandHandler(
    IRequestsDbContext dbContext,
    IWorkContext workContext,
    ISharedDepartmentService departmentService,
    ITicketHistoryService historyService,
    AppSettings appSettings
) : IRequestHandler<CreateTicketCommand, Result<Guid>>
{
    private readonly IRequestsDbContext _dbContext = dbContext;
    private readonly IWorkContext _workContext = workContext;
    private readonly ISharedDepartmentService _departmentService = departmentService;
    private readonly ITicketHistoryService _historyService = historyService;
    private readonly AppSettings _appSettings = appSettings;

    public async Task<Result<Guid>> Handle(CreateTicketCommand request, CancellationToken cancellationToken)
    {
        var ticketId = Guid.NewGuid();

        // Ticket kodu üret - ayarlardan format al
        var ticketCode = CodeGenerator.ProcessFormat(_appSettings.TicketCodeFormat, "Ticket");

        var ticket = new Ticket
        {
            Id = ticketId,
            Code = ticketCode,
            TicketType = request.TicketType,
            TopTicketId = request.TopTicketId,
            SubjectId = request.SubjectId,
            CustomerId = request.CustomerId,
            Title = request.Title,
            Description = request.Description ?? "",
            NotificationWayId = request.NotificationWayId,
            UserId = request.UserId,
            ReporterUserId = _workContext.UserId,
            Priority = request.Priority,
            StatusId = Guid.Empty,
            EndDate = request.EndDate,
            Watchlist = request.Watchlist ?? [],
            Tags = request.Tags
        };

        // Dosyaları ekle
        if (request.TicketFiles.Count > 0)
        {
            ticket.TicketFiles = request.TicketFiles.Select(f => new TicketFile
            {
                Id = Guid.NewGuid(),
                TicketId = ticketId,
                FileId = f.FileId,
                FileName = f.FileName,
                FilePath = f.FilePath
            }).ToList();
        }

        _dbContext.Tickets.Add(ticket);
        if (request.DepartmentIds.Any())
        {
            var departments = (await _departmentService.GetDepartmentsByIdsAsync(request.DepartmentIds)).ToDictionary(x => x.Id, x => x.Name);
            foreach (var departmentId in request.DepartmentIds)
            {
                _dbContext.TicketDepartments.Add(new TicketDepartment
                {
                    Id = Guid.NewGuid(),
                    TicketId = ticket.Id,
                    DepartmentId = departmentId,
                    DepartmentName = departments.TryGetValue(departmentId, out string? value) ? value : "Bilinmeyen Departman",
                });
            }
        }

        // History tracking
        await _historyService.TrackTicketCreatedAsync(ticket.Id, ticket, cancellationToken);

        await _dbContext.SaveChangesAsync(cancellationToken);
        return Result.Success(ticket.Id);
    }
}
