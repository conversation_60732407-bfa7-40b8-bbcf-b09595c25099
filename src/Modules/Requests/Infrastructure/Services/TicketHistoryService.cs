using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Requests.Domain;
using Shared.Application;
using Shared.Contracts;

namespace Requests.Infrastructure.Services;

public class TicketHistoryService : ITicketHistoryService
{
    private readonly IRequestsDbContext _dbContext;
    private readonly IWorkContext _workContext;
    private readonly ISharedUserService _userService;

    public TicketHistoryService(
        IRequestsDbContext dbContext,
        IWorkContext workContext,
        ISharedUserService userService)
    {
        _dbContext = dbContext;
        _workContext = workContext;
        _userService = userService;
    }

    public async Task TrackTicketCreatedAsync(Guid ticketId, Ticket ticket, CancellationToken cancellationToken = default)
    {
        var history = await CreateHistoryEntryAsync(
            ticketId,
            "Ticket",
            null,
            "Ticket oluşturuldu",
            TicketHistoryChangeType.Created,
            $"Ticket oluşturuldu - Başlık: {ticket.Title}");

        _dbContext.TicketHistories.Add(history);
    }

    public async Task TrackTicketUpdatedAsync(Guid ticketId, Ticket oldTicket, Ticket newTicket, CancellationToken cancellationToken = default)
    {
        var changes = new List<TicketHistory>();

        // Title değişikliği
        if (oldTicket.Title != newTicket.Title)
        {
            changes.Add(await CreateHistoryEntryAsync(ticketId, "Title", oldTicket.Title, newTicket.Title, TicketHistoryChangeType.Updated));
        }

        // Description değişikliği
        if (oldTicket.Description != newTicket.Description)
        {
            changes.Add(await CreateHistoryEntryAsync(ticketId, "Description", oldTicket.Description, newTicket.Description, TicketHistoryChangeType.Updated));
        }

        // Priority değişikliği
        if (oldTicket.Priority != newTicket.Priority)
        {
            changes.Add(await CreateHistoryEntryAsync(ticketId, "Priority",
                oldTicket.Priority.ToString(),
                newTicket.Priority.ToString(),
                TicketHistoryChangeType.Updated));
        }

        // EndDate değişikliği
        if (oldTicket.EndDate != newTicket.EndDate)
        {
            changes.Add(await CreateHistoryEntryAsync(ticketId, "EndDate",
                oldTicket.EndDate?.ToString("dd.MM.yyyy HH:mm"),
                newTicket.EndDate?.ToString("dd.MM.yyyy HH:mm"),
                TicketHistoryChangeType.Updated));
        }

        // NotificationWayId değişikliği
        if (oldTicket.NotificationWayId != newTicket.NotificationWayId)
        {
            changes.Add(await CreateHistoryEntryAsync(ticketId, "NotificationWay",
                oldTicket.NotificationWayId?.ToString(),
                newTicket.NotificationWayId?.ToString(),
                TicketHistoryChangeType.Updated));
        }

        // SubjectId değişikliği
        if (oldTicket.SubjectId != newTicket.SubjectId)
        {
            changes.Add(await CreateHistoryEntryAsync(ticketId, "Subject",
                oldTicket.SubjectId.ToString(),
                newTicket.SubjectId.ToString(),
                TicketHistoryChangeType.Updated));
        }

        foreach (var change in changes)
        {
            _dbContext.TicketHistories.Add(change);
        }
    }

    public async Task TrackTicketAssignedAsync(Guid ticketId, Guid? oldUserId, Guid? newUserId, CancellationToken cancellationToken = default)
    {
        string oldUserName = null;
        string newUserName = null;

        if (oldUserId.HasValue)
        {
            var oldUser = await _userService.GetUserAsync(oldUserId.Value);
            if (oldUser.IsSuccess)
                oldUserName = $"{oldUser.Value.Name} {oldUser.Value.Surname}";
        }

        if (newUserId.HasValue)
        {
            var newUser = await _userService.GetUserAsync(newUserId.Value);
            if (newUser.IsSuccess)
                newUserName = $"{newUser.Value.Name} {newUser.Value.Surname}";
        }

        var history = await CreateHistoryEntryAsync(ticketId, "AssignedUser",
            oldUserName ?? "Atanmamış",
            newUserName ?? "Atanmamış",
            TicketHistoryChangeType.Assigned);

        _dbContext.TicketHistories.Add(history);
    }

    public async Task TrackTicketStatusChangedAsync(Guid ticketId, Guid? oldStatusId, Guid? newStatusId, CancellationToken cancellationToken = default)
    {
        string oldStatusName = "Belirlenmemiş";
        string newStatusName = "Belirlenmemiş";

        if (oldStatusId.HasValue)
        {
            var oldStatus = await _dbContext.Nodes.FirstOrDefaultAsync(n => n.Id == oldStatusId.Value, cancellationToken);
            if (oldStatus != null)
                oldStatusName = oldStatus.Name;
        }

        if (newStatusId.HasValue)
        {
            var newStatus = await _dbContext.Nodes.FirstOrDefaultAsync(n => n.Id == newStatusId.Value, cancellationToken);
            if (newStatus != null)
                newStatusName = newStatus.Name;
        }

        var history = await CreateHistoryEntryAsync(ticketId, "Status", oldStatusName, newStatusName, TicketHistoryChangeType.StatusChanged);
        _dbContext.TicketHistories.Add(history);
    }

    public async Task TrackTicketCommentAddedAsync(Guid ticketId, string comment, CancellationToken cancellationToken = default)
    {
        var history = await CreateHistoryEntryAsync(ticketId, "Comment", null, comment, TicketHistoryChangeType.CommentAdded, "Yorum eklendi");
        _dbContext.TicketHistories.Add(history);
    }

    public async Task TrackTicketFileAddedAsync(Guid ticketId, string fileName, CancellationToken cancellationToken = default)
    {
        var history = await CreateHistoryEntryAsync(ticketId, "File", null, fileName, TicketHistoryChangeType.FileAdded, "Dosya eklendi");
        _dbContext.TicketHistories.Add(history);
    }

    public async Task TrackTicketFileRemovedAsync(Guid ticketId, string fileName, CancellationToken cancellationToken = default)
    {
        var history = await CreateHistoryEntryAsync(ticketId, "File", fileName, null, TicketHistoryChangeType.FileRemoved, "Dosya silindi");
        _dbContext.TicketHistories.Add(history);
    }

    public async Task TrackTicketDepartmentChangedAsync(Guid ticketId, List<Guid> oldDepartmentIds, List<Guid> newDepartmentIds, CancellationToken cancellationToken = default)
    {
        var oldDepartmentsJson = JsonSerializer.Serialize(oldDepartmentIds);
        var newDepartmentsJson = JsonSerializer.Serialize(newDepartmentIds);

        var history = await CreateHistoryEntryAsync(ticketId, "Departments", oldDepartmentsJson, newDepartmentsJson, TicketHistoryChangeType.DepartmentChanged, "Departmanlar değiştirildi");
        _dbContext.TicketHistories.Add(history);
    }

    public async Task TrackTicketWatchlistChangedAsync(Guid ticketId, List<Guid> oldWatchlist, List<Guid> newWatchlist, CancellationToken cancellationToken = default)
    {
        var oldWatchlistJson = JsonSerializer.Serialize(oldWatchlist);
        var newWatchlistJson = JsonSerializer.Serialize(newWatchlist);

        var history = await CreateHistoryEntryAsync(ticketId, "Watchlist", oldWatchlistJson, newWatchlistJson, TicketHistoryChangeType.WatchlistChanged, "İzleme listesi değiştirildi");
        _dbContext.TicketHistories.Add(history);
    }

    public async Task TrackTicketTagsChangedAsync(Guid ticketId, List<string> oldTags, List<string> newTags, CancellationToken cancellationToken = default)
    {
        var oldTagsJson = JsonSerializer.Serialize(oldTags);
        var newTagsJson = JsonSerializer.Serialize(newTags);

        var history = await CreateHistoryEntryAsync(ticketId, "Tags", oldTagsJson, newTagsJson, TicketHistoryChangeType.TagsChanged, "Etiketler değiştirildi");
        _dbContext.TicketHistories.Add(history);
    }

    private async Task<TicketHistory> CreateHistoryEntryAsync(Guid ticketId, string fieldName, string? oldValue, string? newValue, TicketHistoryChangeType changeType, string? description = null)
    {
        var currentUser = await GetCurrentUserNameAsync();

        return new TicketHistory
        {
            Id = Guid.NewGuid(),
            TicketId = ticketId,
            FieldName = fieldName,
            OldValue = TruncateValue(oldValue),
            NewValue = TruncateValue(newValue),
            ChangeType = changeType,
            Description = description,
            ChangedByUserId = _workContext.UserId == Guid.Empty ? null : _workContext.UserId,
            ChangedByUserName = currentUser
        };
    }

    private async Task<string> GetCurrentUserNameAsync()
    {
        if (_workContext.UserId == Guid.Empty)
            return "Sistem";

        try
        {
            var userResult = await _userService.GetUserAsync(_workContext.UserId);
            return userResult.IsSuccess ? $"{userResult.Value.Name} {userResult.Value.Surname}" : "Bilinmeyen Kullanıcı";
        }
        catch
        {
            return "Bilinmeyen Kullanıcı";
        }
    }

    private static string? TruncateValue(string? value)
    {
        if (string.IsNullOrEmpty(value))
            return value;

        return value.Length > 500 ? value[..500] + "..." : value;
    }
}
