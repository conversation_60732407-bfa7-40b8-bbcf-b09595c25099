using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Requests.Domain;

namespace Requests.Infrastructure.Data.Configurations;

public class TicketHistoryConfiguration : IEntityTypeConfiguration<TicketHistory>
{
    public void Configure(EntityTypeBuilder<TicketHistory> builder)
    {
        builder.ToTable("TicketHistories");

        builder.HasKey(x => x.Id);

        builder.Property(x => x.TicketId)
            .IsRequired();

        builder.Property(x => x.FieldName)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(x => x.OldValue)
            .HasMaxLength(2000);

        builder.Property(x => x.NewValue)
            .HasMaxLength(2000);

        builder.Property(x => x.ChangeType)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(x => x.Description)
            .HasMaxLength(500);

        // Ticket ile ilişki
        builder.HasOne(x => x.Ticket)
            .WithMany()
            .HasForeignKey(x => x.TicketId)
            .OnDelete(DeleteBehavior.Cascade);

        // Index'ler
        builder.HasIndex(x => x.TicketId);
        builder.HasIndex(x => x.InsertDate);
        builder.HasIndex(x => new { x.TicketId, x.InsertDate });
    }
}
