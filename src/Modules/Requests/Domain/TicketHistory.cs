using Shared.Domain;

namespace Requests.Domain;

public class TicketHistory : AuditableEntity
{
    public Guid Id { get; set; }
    public Guid TicketId { get; set; }
    public Ticket? Ticket { get; set; }
    public string FieldName { get; set; } = string.Empty;
    public string? OldValue { get; set; }
    public string? NewValue { get; set; }
    public TicketHistoryChangeType ChangeType { get; set; }
    public string? Description { get; set; }
}

public enum TicketHistoryChangeType
{
    Created = 1,
    Updated = 2,
    Assigned = 3,
    StatusChanged = 4,
    CommentAdded = 5,
    FileAdded = 6,
    FileRemoved = 7,
    DepartmentChanged = 8,
    WatchlistChanged = 9,
    TagsChanged = 10
}
