@baseUrl = https://localhost:7001
@ticketId = {{$guid}}

### Ticket Oluştur (History için)
POST {{baseUrl}}/api/v1/requests/tickets
Content-Type: application/json
Authorization: Bearer {{$auth.token}}

{
  "ticketType": 1,
  "subjectId": "{{$guid}}",
  "customerId": "{{$guid}}",
  "title": "Test Ticket - History",
  "description": "Bu ticket history testi için oluşturuldu",
  "priority": 2,
  "departmentIds": [],
  "ticketFiles": [],
  "tags": ["test", "history"]
}

### Ticket Güncelle (History için)
PUT {{baseUrl}}/api/v1/requests/tickets/{{ticketId}}
Content-Type: application/json
Authorization: Bearer {{$auth.token}}

{
  "id": "{{ticketId}}",
  "ticketType": 1,
  "subjectId": "{{$guid}}",
  "title": "Test Ticket - History Güncellendi",
  "description": "Bu ticket history testi için güncellendi",
  "priority": 3,
  "departmentIds": [],
  "ticketFiles": [],
  "tags": ["test", "history", "updated"]
}

### Ticket Ata (History için)
POST {{baseUrl}}/api/v1/requests/tickets/{{ticketId}}/assign
Content-Type: application/json
Authorization: Bearer {{$auth.token}}

{
  "ticketId": "{{ticketId}}",
  "userId": "{{$guid}}"
}

### Ticket Yorum Ekle (History için)
POST {{baseUrl}}/api/v1/requests/tickets/{{ticketId}}/comments
Content-Type: application/json
Authorization: Bearer {{$auth.token}}

{
  "ticketId": "{{ticketId}}",
  "comment": "Bu bir test yorumudur - history için"
}

### Ticket History Getir
GET {{baseUrl}}/api/v1/requests/tickets/{{ticketId}}/history
Authorization: Bearer {{$auth.token}}

### Ticket Detay Getir (History ile birlikte kontrol için)
GET {{baseUrl}}/api/v1/requests/tickets/{{ticketId}}
Authorization: Bearer {{$auth.token}}
